// Create admin user directly in database
const { User } = require('./src/models');
const { connectDB } = require('./src/config/database');

async function createAdmin() {
  try {
    // Connect to database
    await connectDB();
    
    console.log('🔗 Connected to database');
    
    // Check if admin already exists
    const existingAdmin = await User.findOne({ where: { email: '<EMAIL>' } });
    
    if (existingAdmin) {
      console.log('✅ Admin user already exists');
      console.log('Admin:', existingAdmin.toJSON());
      return existingAdmin;
    }
    
    // Create admin user
    const adminData = {
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'AdminPass123',
      phone: '+966501234568',
      role: 'admin',
      isEmailVerified: true,
      active: true
    };
    
    const admin = await User.create(adminData);
    
    console.log('✅ Admin user created successfully!');
    console.log('Admin:', admin.toJSON());
    
    return admin;
    
  } catch (error) {
    console.error('❌ Error creating admin:', error);
  } finally {
    process.exit(0);
  }
}

createAdmin();
