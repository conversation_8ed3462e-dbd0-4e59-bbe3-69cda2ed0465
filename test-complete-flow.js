// Test Complete Registration and Analytics Flow
const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function testCompleteFlow() {
  try {
    console.log('🧪 Testing Complete Store Registration and Analytics Flow...\n');
    
    // Generate unique data
    const timestamp = Date.now();
    const uniqueEmail = `store${timestamp}@example.com`;
    const uniqueDomain = `store-${timestamp}`;
    
    // 1. Test store registration
    console.log('1. Testing store registration...');
    const registerData = {
      name: 'Store Owner',
      email: uniqueEmail,
      password: 'StorePass123',
      passwordConfirm: 'StorePass123',
      phone: '+1234567890',
      role: 'admin',
      storeName: `Amazing Store ${timestamp}`,
      storeDomain: uniqueDomain,
      storeDescription: 'This is an amazing store for testing'
    };
    
    const registerResponse = await axios.post(`${API_BASE}/auth/register`, registerData);
    console.log('✅ Store registration successful');
    console.log(`   Store: ${registerData.storeName}`);
    console.log(`   Domain: ${registerData.storeDomain}.salla.com`);
    console.log(`   Owner: ${registerData.email}`);
    
    // 2. Test login
    console.log('\n2. Testing login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: registerData.email,
      password: registerData.password
    });
    
    const token = loginResponse.data.token;
    const user = loginResponse.data.data.user;
    console.log('✅ Login successful');
    console.log(`   User: ${user.name} (${user.role})`);
    
    // 3. Test analytics (should work now with store_id)
    console.log('\n3. Testing store analytics...');
    const analyticsResponse = await axios.get(`${API_BASE}/analytics/dashboard`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Analytics retrieved successfully');
    console.log('   Dashboard Stats:');
    analyticsResponse.data.data.stats.forEach(stat => {
      console.log(`   - ${stat.title}: ${stat.value} (${stat.change})`);
    });
    
    // 4. Test sales data
    console.log('\n4. Testing sales data...');
    const salesResponse = await axios.get(`${API_BASE}/analytics/sales?period=7d`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Sales data retrieved');
    console.log(`   Period: ${salesResponse.data.data.period}`);
    console.log(`   Data points: ${salesResponse.data.data.salesData.length}`);
    
    // 5. Test top products
    console.log('\n5. Testing top products...');
    const productsResponse = await axios.get(`${API_BASE}/analytics/top-products?limit=5`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Top products retrieved');
    console.log(`   Products found: ${productsResponse.data.data.topProducts.length}`);
    
    // 6. Test customer stats
    console.log('\n6. Testing customer stats...');
    const customerResponse = await axios.get(`${API_BASE}/analytics/customer-stats`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Customer stats retrieved');
    const stats = customerResponse.data.data.customerStats;
    console.log(`   Total customers: ${stats.totalCustomers}`);
    console.log(`   New customers: ${stats.newCustomers}`);
    console.log(`   Active customers: ${stats.activeCustomers}`);
    console.log(`   Growth rate: ${stats.growthRate}`);
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Complete Flow Summary:');
    console.log('✅ Multi-step store registration');
    console.log('✅ Store creation with unique domain');
    console.log('✅ Admin authentication');
    console.log('✅ Store-specific analytics');
    console.log('✅ Dashboard statistics');
    console.log('✅ Sales data charts');
    console.log('✅ Top products tracking');
    console.log('✅ Customer analytics');
    console.log('✅ CORS configuration');
    console.log('✅ Database multi-store support');
    
    console.log('\n🌐 Access Your New Store:');
    console.log(`- Admin Dashboard: http://localhost:3002`);
    console.log(`- Login Email: ${registerData.email}`);
    console.log(`- Login Password: ${registerData.password}`);
    console.log(`- Store Domain: ${registerData.storeDomain}.salla.com`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testCompleteFlow();
