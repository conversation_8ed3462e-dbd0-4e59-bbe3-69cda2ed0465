// Simple login test
const axios = require('axios');

async function testSimpleLogin() {
  try {
    console.log('🧪 Testing Simple Login Flow...\n');
    
    // Test login
    console.log('1. Testing login...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'AdminPass123'
    });
    
    if (loginResponse.data.success) {
      console.log('✅ Login successful');
      console.log(`   User: ${loginResponse.data.data.user.name}`);
      console.log(`   Role: ${loginResponse.data.data.user.role}`);
      console.log(`   Token: ${loginResponse.data.token ? 'Received' : 'Missing'}`);
      
      // Test dashboard access
      console.log('\n2. Testing dashboard access...');
      const dashboardResponse = await axios.get('http://localhost:3001/api/analytics/dashboard', {
        headers: { Authorization: `Bearer ${loginResponse.data.token}` }
      });
      
      if (dashboardResponse.data.success) {
        console.log('✅ Dashboard access successful');
        console.log(`   Stats count: ${dashboardResponse.data.data.stats.length}`);
      }
      
      console.log('\n🎉 All tests passed!');
      console.log('\n📋 Next Steps:');
      console.log('1. Open http://localhost:3000 in your browser');
      console.log('2. Login with: <EMAIL> / AdminPass123');
      console.log('3. You should see "Welcome back, Admin!" message');
      console.log('4. After 1 second, you should be redirected to dashboard');
      console.log('5. Check browser console for detailed logs');
      
    } else {
      console.log('❌ Login failed:', loginResponse.data.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
  }
}

testSimpleLogin();
