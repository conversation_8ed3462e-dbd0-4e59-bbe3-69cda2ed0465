// Simple API test script
const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

// Test user registration
async function testRegister() {
  try {
    console.log('🧪 Testing user registration...');
    
    const userData = {
      name: '<PERSON>',
      email: '<EMAIL>',
      password: 'Password123',
      passwordConfirm: 'Password123',
      phone: '+966501234567',
      role: 'customer'
    };

    const response = await axios.post(`${API_BASE}/auth/register`, userData);
    
    console.log('✅ Registration successful!');
    console.log('Response:', response.data);
    
    return response.data.token;
  } catch (error) {
    console.log('❌ Registration failed:');
    console.log('Error:', error.response?.data || error.message);
    return null;
  }
}

// Test user login
async function testLogin() {
  try {
    console.log('\n🧪 Testing user login...');
    
    const loginData = {
      email: '<EMAIL>',
      password: 'Password123'
    };

    const response = await axios.post(`${API_BASE}/auth/login`, loginData);
    
    console.log('✅ Login successful!');
    console.log('Response:', response.data);
    
    return response.data.token;
  } catch (error) {
    console.log('❌ Login failed:');
    console.log('Error:', error.response?.data || error.message);
    return null;
  }
}

// Test get user profile
async function testProfile(token) {
  try {
    console.log('\n🧪 Testing get user profile...');
    
    const response = await axios.get(`${API_BASE}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Profile retrieved successfully!');
    console.log('User:', response.data.data.user);
    
    return response.data.data.user;
  } catch (error) {
    console.log('❌ Profile retrieval failed:');
    console.log('Error:', error.response?.data || error.message);
    return null;
  }
}

// Test create category
async function testCreateCategory(token) {
  try {
    console.log('\n🧪 Testing create category...');
    
    const categoryData = {
      name: 'Electronics',
      description: 'Electronic devices and gadgets',
      isActive: true,
      isFeatured: true
    };

    const response = await axios.post(`${API_BASE}/categories`, categoryData, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Category created successfully!');
    console.log('Category:', response.data.data.category);
    
    return response.data.data.category;
  } catch (error) {
    console.log('❌ Category creation failed:');
    console.log('Error:', error.response?.data || error.message);
    return null;
  }
}

// Test get categories
async function testGetCategories() {
  try {
    console.log('\n🧪 Testing get categories...');
    
    const response = await axios.get(`${API_BASE}/categories`);
    
    console.log('✅ Categories retrieved successfully!');
    console.log('Categories:', response.data.data.categories);
    
    return response.data.data.categories;
  } catch (error) {
    console.log('❌ Categories retrieval failed:');
    console.log('Error:', error.response?.data || error.message);
    return null;
  }
}

// Test health check
async function testHealth() {
  try {
    console.log('\n🧪 Testing health check...');
    
    const response = await axios.get('http://localhost:3000/health');
    
    console.log('✅ Health check successful!');
    console.log('Status:', response.data);
    
    return true;
  } catch (error) {
    console.log('❌ Health check failed:');
    console.log('Error:', error.response?.data || error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('🚀 Starting API Tests...\n');
  
  // Test health check first
  await testHealth();
  
  // Test user registration
  let token = await testRegister();
  
  // If registration fails, try login
  if (!token) {
    token = await testLogin();
  }
  
  // Test profile if we have a token
  if (token) {
    await testProfile(token);
    
    // Test category creation (requires admin/merchant role)
    await testCreateCategory(token);
  }
  
  // Test get categories (public endpoint)
  await testGetCategories();
  
  console.log('\n🏁 Tests completed!');
}

// Run the tests
runTests().catch(console.error);
