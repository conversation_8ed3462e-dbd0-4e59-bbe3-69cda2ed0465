import axios from 'axios';

// Create axios instance with proper URL validation
const getBaseURL = () => {
  const envURL = process.env.REACT_APP_API_URL;
  const defaultURL = 'http://localhost:8000/api'; // Updated to correct port

  // Validate URL
  try {
    if (envURL) {
      new URL(envURL);
      return envURL;
    }
  } catch (error) {
    console.warn('Invalid REACT_APP_API_URL, using default:', error.message);
  }

  return defaultURL;
};

const api = axios.create({
  baseURL: getBaseURL(),
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Token will be added by individual API calls
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling with improved retry logic
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  async (error) => {
    const originalRequest = error.config;

    console.error('API Error:', error);

    // Handle rate limiting (429) with exponential backoff
    if (error.response?.status === 429 && !originalRequest._retry) {
      originalRequest._retry = true;
      originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;

      // Max 3 retries
      if (originalRequest._retryCount <= 3) {
        const delay = Math.pow(2, originalRequest._retryCount) * 1000; // 2s, 4s, 8s
        console.log(`Rate limited, waiting ${delay}ms before retry ${originalRequest._retryCount}/3...`);

        await new Promise(resolve => setTimeout(resolve, delay));

        // Retry the request
        return api.request(originalRequest);
      } else {
        console.error('Max retries reached for rate limiting');
        error.message = 'Server is busy. Please try again in a few minutes.';
      }
    }

    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('admin-auth-storage');
      window.location.href = '/login';
    }

    // Enhance error message for better user experience
    if (error.response?.data) {
      const errorData = error.response.data;

      // If there's a specific message, use it
      if (errorData.message) {
        error.message = errorData.message;
      } else if (errorData.error?.message) {
        error.message = errorData.error.message;
      }
    }

    // Handle specific error codes
    if (error.response?.status === 429 && !originalRequest._retry) {
      error.message = 'Too many requests. Please wait a moment and try again.';
    }

    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (emailOrPhone, password) => {
    // Determine if input is email or phone
    const isEmail = emailOrPhone.includes('@');
    const isPhone = emailOrPhone.startsWith('+966');

    let loginData;
    if (isEmail) {
      loginData = { email: emailOrPhone, password };
    } else if (isPhone) {
      loginData = { phone: emailOrPhone, password };
    } else {
      // Try as email first, then phone
      loginData = { emailOrPhone, password };
    }

    console.log('🔐 Login attempt with:', isEmail ? 'Email' : isPhone ? 'Phone' : 'Email/Phone', emailOrPhone);

    const response = await api.post('/auth/login', loginData);

    console.log('🔍 Raw API response:', response);

    // If login successful, force redirect after a short delay
    if (response.success) {
      console.log('✅ Login successful, preparing redirect...');

      // Use multiple methods to ensure redirect works
      setTimeout(() => {
        console.log('🔄 Executing redirect to dashboard...');

        // Method 1: Try React Router navigation
        if (window.history && window.history.pushState) {
          window.history.pushState({}, '', '/dashboard');
          window.dispatchEvent(new PopStateEvent('popstate'));
        }

        // Method 2: Fallback to location change
        setTimeout(() => {
          if (window.location.pathname !== '/dashboard') {
            console.log('🔄 Fallback redirect method...');
            window.location.replace('/dashboard');
          }
        }, 500);

      }, 1000);
    }

    return response;
  },

  register: async (userData) => {
    return await api.post('/auth/register', userData);
  },

  logout: async (token) => {
    return await api.post('/auth/logout', {}, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getProfile: async (token) => {
    return await api.get('/auth/me', {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  updateProfile: async (userData, token) => {
    return await api.patch('/auth/update-profile', userData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
};

// Products API
export const productsAPI = {
  getProducts: async (params = {}, token) => {
    return await api.get('/products', {
      params,
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getProduct: async (id, token) => {
    return await api.get(`/products/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  createProduct: async (productData, token) => {
    return await api.post('/products', productData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  updateProduct: async (id, productData, token) => {
    return await api.patch(`/products/${id}`, productData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  deleteProduct: async (id, token) => {
    return await api.delete(`/products/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
};

// Categories API
export const categoriesAPI = {
  getCategories: async (token) => {
    return await api.get('/categories', {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getCategory: async (id, token) => {
    return await api.get(`/categories/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  createCategory: async (categoryData, token) => {
    return await api.post('/categories', categoryData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  updateCategory: async (id, categoryData, token) => {
    return await api.patch(`/categories/${id}`, categoryData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  deleteCategory: async (id, token) => {
    return await api.delete(`/categories/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
};

// Orders API
export const ordersAPI = {
  getOrders: async (params = {}, token) => {
    return await api.get('/orders', {
      params,
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getOrder: async (id, token) => {
    return await api.get(`/orders/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  updateOrderStatus: async (id, status, token) => {
    return await api.patch(`/orders/${id}/status`, { status }, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  cancelOrder: async (id, reason, token) => {
    return await api.patch(`/orders/${id}/cancel`, { reason }, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
};

// Customers API
export const customersAPI = {
  getCustomers: async (params = {}, token) => {
    return await api.get('/customers', {
      params,
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getCustomer: async (id, token) => {
    return await api.get(`/customers/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  updateCustomer: async (id, customerData, token) => {
    return await api.patch(`/customers/${id}`, customerData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  deleteCustomer: async (id, token) => {
    return await api.delete(`/customers/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
};

// Coupons API
export const couponsAPI = {
  getCoupons: async (params = {}, token) => {
    return await api.get('/coupons', {
      params,
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getCoupon: async (id, token) => {
    return await api.get(`/coupons/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  createCoupon: async (couponData, token) => {
    return await api.post('/coupons', couponData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  updateCoupon: async (id, couponData, token) => {
    return await api.patch(`/coupons/${id}`, couponData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  deleteCoupon: async (id, token) => {
    return await api.delete(`/coupons/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
};

// Analytics API
export const analyticsAPI = {
  getDashboardStats: async (token) => {
    return await api.get('/analytics/dashboard', {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getSalesData: async (period, token) => {
    return await api.get(`/analytics/sales?period=${period}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getTopProducts: async (limit = 10, token) => {
    return await api.get(`/analytics/top-products?limit=${limit}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getRecentOrders: async (limit = 5, token) => {
    return await api.get(`/analytics/recent-orders?limit=${limit}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getCustomerStats: async (token) => {
    return await api.get('/analytics/customer-stats', {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
};

// Store API
export const storeAPI = {
  getSettings: async (token) => {
    return await api.get('/store/settings', {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  updateSettings: async (settings, token) => {
    return await api.patch('/store/settings', settings, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
};

// Payments API
export const paymentsAPI = {
  getAll: async (params = {}, token) => {
    return await api.get('/payments', {
      params,
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getById: async (id, token) => {
    return await api.get(`/payments/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getMethods: async (token) => {
    return await api.get('/payments/methods', {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  updateMethod: async (id, methodData, token) => {
    return await api.put(`/payments/methods/${id}`, methodData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  processRefund: async (paymentId, refundData, token) => {
    return await api.post(`/payments/${paymentId}/refund`, refundData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
};

// Shipping API
export const shippingAPI = {
  getMethods: async (token) => {
    return await api.get('/shipping/methods', {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  createMethod: async (methodData, token) => {
    return await api.post('/shipping/methods', methodData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  updateMethod: async (id, methodData, token) => {
    return await api.put(`/shipping/methods/${id}`, methodData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  deleteMethod: async (id, token) => {
    return await api.delete(`/shipping/methods/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  getZones: async (token) => {
    return await api.get('/shipping/zones', {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  createZone: async (zoneData, token) => {
    return await api.post('/shipping/zones', zoneData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  updateZone: async (id, zoneData, token) => {
    return await api.put(`/shipping/zones/${id}`, zoneData, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },

  deleteZone: async (id, token) => {
    return await api.delete(`/shipping/zones/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
  },
};

export default api;
