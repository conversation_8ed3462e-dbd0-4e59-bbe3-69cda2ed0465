import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { NavLink, useLocation } from 'react-router-dom';
import {
  X,
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  Tag,
  BarChart3,
  Settings,
  ShoppingBag,
  Percent,
  FileText,
  TrendingUp,
  CreditCard,
  Truck
} from 'lucide-react';

import { useLanguage } from '../../contexts/LanguageContext';
import { useStore } from '../../contexts/StoreContext';

const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation();
  const { t, isRTL } = useLanguage();
  const { storeSettings } = useStore();

  const navigation = [
    {
      name: t('dashboard'),
      href: '/dashboard',
      icon: LayoutDashboard,
      current: location.pathname === '/dashboard',
    },
    {
      name: t('products'),
      href: '/products',
      icon: Package,
      current: location.pathname.startsWith('/products'),
    },
    {
      name: 'Categories',
      href: '/categories',
      icon: Tag,
      current: location.pathname === '/categories',
    },
    {
      name: t('orders'),
      href: '/orders',
      icon: ShoppingCart,
      current: location.pathname.startsWith('/orders'),
    },
    {
      name: t('customers'),
      href: '/customers',
      icon: Users,
      current: location.pathname.startsWith('/customers'),
    },
    {
      name: 'Payments',
      href: '/payments',
      icon: CreditCard,
      current: location.pathname.startsWith('/payments'),
    },
    {
      name: 'Shipping',
      href: '/shipping',
      icon: Truck,
      current: location.pathname.startsWith('/shipping'),
    },
    {
      name: 'Coupons',
      href: '/coupons',
      icon: Percent,
      current: location.pathname === '/coupons',
    },
    {
      name: t('analytics'),
      href: '/analytics',
      icon: BarChart3,
      current: location.pathname === '/analytics',
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      current: location.pathname === '/settings',
    },
  ];

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: isRTL ? "100%" : "-100%",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <div className={`hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col ${isRTL ? 'lg:right-0' : 'lg:left-0'}`}>
        <div className={`flex min-h-0 flex-1 flex-col bg-white ${isRTL ? 'border-l' : 'border-r'} border-gray-200`}>
          {/* Logo */}
          <div className="flex h-16 flex-shrink-0 items-center px-6 border-b border-gray-200">
            <div className="flex items-center">
              {storeSettings.logo ? (
                <img
                  src={storeSettings.logo}
                  alt={storeSettings.name || 'Store Logo'}
                  className={`h-8 w-8 rounded-lg object-cover ${isRTL ? 'ml-3' : 'mr-3'}`}
                />
              ) : (
                <div className={`h-8 w-8 bg-gradient-primary rounded-lg flex items-center justify-center ${isRTL ? 'ml-3' : 'mr-3'}`}>
                  <ShoppingBag className="h-5 w-5 text-white" />
                </div>
              )}
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  {storeSettings.name || 'Salla Admin'}
                </h1>
                <p className="text-xs text-gray-500">E-commerce Dashboard</p>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 space-y-1 px-4 py-6 custom-scrollbar overflow-y-auto">
            {navigation.map((item) => (
              <NavLink
                key={item.name}
                to={item.href}
                className={({ isActive }) =>
                  `group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                    isActive
                      ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-600'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-primary-600'
                  }`
                }
              >
                <item.icon
                  className={`mr-3 h-5 w-5 flex-shrink-0 transition-colors duration-200 ${
                    item.current
                      ? 'text-primary-600'
                      : 'text-gray-400 group-hover:text-primary-500'
                  }`}
                />
                {item.name}
              </NavLink>
            ))}
          </nav>

          {/* Bottom Section */}
          <div className="flex-shrink-0 border-t border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-white" />
                </div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-700">Store Performance</p>
                <p className="text-xs text-success-600 font-medium">+12.5% this month</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial="closed"
            animate="open"
            exit="closed"
            variants={sidebarVariants}
            className="fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-gray-200 lg:hidden"
          >
            <div className="flex min-h-0 flex-1 flex-col">
              {/* Header */}
              <div className="flex h-16 flex-shrink-0 items-center justify-between px-6 border-b border-gray-200">
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-gradient-primary rounded-lg flex items-center justify-center mr-3">
                    <ShoppingBag className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h1 className="text-xl font-bold text-gray-900">Salla Admin</h1>
                    <p className="text-xs text-gray-500">E-commerce Dashboard</p>
                  </div>
                </div>
                <button
                  type="button"
                  className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
                  onClick={onClose}
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              {/* Navigation */}
              <nav className="flex-1 space-y-1 px-4 py-6 custom-scrollbar overflow-y-auto">
                {navigation.map((item) => (
                  <NavLink
                    key={item.name}
                    to={item.href}
                    onClick={onClose}
                    className={({ isActive }) =>
                      `group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                        isActive
                          ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-600'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-primary-600'
                      }`
                    }
                  >
                    <item.icon
                      className={`mr-3 h-5 w-5 flex-shrink-0 transition-colors duration-200 ${
                        item.current
                          ? 'text-primary-600'
                          : 'text-gray-400 group-hover:text-primary-500'
                      }`}
                    />
                    {item.name}
                  </NavLink>
                ))}
              </nav>

              {/* Bottom Section */}
              <div className="flex-shrink-0 border-t border-gray-200 p-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-gradient-primary flex items-center justify-center">
                      <TrendingUp className="h-5 w-5 text-white" />
                    </div>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-700">Store Performance</p>
                    <p className="text-xs text-success-600 font-medium">+12.5% this month</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default Sidebar;
