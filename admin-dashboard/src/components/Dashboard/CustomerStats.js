import React from 'react';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import { Users, UserPlus, Activity, TrendingUp } from 'lucide-react';

import { analyticsAPI } from '../../services/api';
import { useAuthStore } from '../../store/authStore';
import LoadingSpinner from '../UI/LoadingSpinner';

const CustomerStats = () => {
  const { getToken } = useAuthStore();

  const { data: customerResponse, isLoading } = useQuery(
    'customerStats',
    () => analyticsAPI.getCustomerStats(getToken()),
    {
      refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
    }
  );

  const stats = customerResponse?.data?.customerStats || {};

  const customerMetrics = [
    {
      title: 'Total Customers',
      value: stats.totalCustomers || 0,
      icon: Users,
      color: 'primary',
      description: 'All registered customers'
    },
    {
      title: 'New This Month',
      value: stats.newCustomers || 0,
      icon: UserPlus,
      color: 'success',
      description: 'Recently joined'
    },
    {
      title: 'Active Customers',
      value: stats.activeCustomers || 0,
      icon: Activity,
      color: 'info',
      description: 'Ordered recently'
    }
  ];

  const getColorClasses = (color) => {
    const colorMap = {
      primary: {
        bg: 'bg-primary-50',
        icon: 'text-primary-600',
        text: 'text-primary-600'
      },
      success: {
        bg: 'bg-success-50',
        icon: 'text-success-600',
        text: 'text-success-600'
      },
      info: {
        bg: 'bg-info-50',
        icon: 'text-info-600',
        text: 'text-info-600'
      },
      warning: {
        bg: 'bg-warning-50',
        icon: 'text-warning-600',
        text: 'text-warning-600'
      }
    };
    return colorMap[color] || colorMap.primary;
  };

  return (
    <div className="card p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">Customer Overview</h3>
          <p className="text-sm text-gray-600">Customer growth and activity</p>
        </div>
        <a
          href="/customers"
          className="text-sm font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200"
        >
          View all
        </a>
      </div>

      {/* Stats */}
      {isLoading ? (
        <div className="flex items-center justify-center h-48">
          <LoadingSpinner size="lg" />
        </div>
      ) : (
        <div className="space-y-6">
          {/* Customer Metrics */}
          <div className="space-y-4">
            {customerMetrics.map((metric, index) => {
              const colors = getColorClasses(metric.color);
              return (
                <motion.div
                  key={metric.title}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${colors.bg}`}>
                      <metric.icon className={`h-5 w-5 ${colors.icon}`} />
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">
                        {metric.title}
                      </h4>
                      <p className="text-xs text-gray-600">
                        {metric.description}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-gray-900">
                      {metric.value.toLocaleString()}
                    </p>
                  </div>
                </motion.div>
              );
            })}
          </div>

          {/* Growth Rate */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
            className="p-4 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-lg border border-primary-100"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-primary-100 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-primary-600" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-gray-900">
                    Growth Rate
                  </h4>
                  <p className="text-xs text-gray-600">
                    Customer growth this month
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className={`text-lg font-bold ${
                  stats.growthRate && stats.growthRate.startsWith('+') 
                    ? 'text-success-600' 
                    : stats.growthRate && stats.growthRate.startsWith('-')
                    ? 'text-error-600'
                    : 'text-gray-600'
                }`}>
                  {stats.growthRate || '0%'}
                </p>
                <p className="text-xs text-gray-600">
                  vs last month
                </p>
              </div>
            </div>
          </motion.div>

          {/* Customer Activity Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
            className="pt-4 border-t border-gray-200"
          >
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.activeCustomers && stats.totalCustomers 
                    ? Math.round((stats.activeCustomers / stats.totalCustomers) * 100)
                    : 0}%
                </p>
                <p className="text-xs text-gray-600">Active Rate</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.newCustomers || 0}
                </p>
                <p className="text-xs text-gray-600">New This Month</p>
              </div>
            </div>
          </motion.div>
        </div>
      )}

      {/* Footer */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            Updated in real-time
          </span>
          <a
            href="/customers"
            className="font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200"
          >
            Manage customers →
          </a>
        </div>
      </div>
    </div>
  );
};

export default CustomerStats;
