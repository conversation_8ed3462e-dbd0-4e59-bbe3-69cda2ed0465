import React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown } from 'lucide-react';

const StatsCard = ({ 
  title, 
  value, 
  change, 
  trend, 
  icon: Icon, 
  color = 'primary',
  description 
}) => {
  const colorClasses = {
    primary: {
      bg: 'bg-primary-50',
      icon: 'text-primary-600',
      trend: trend === 'up' ? 'text-success-600' : 'text-error-600'
    },
    success: {
      bg: 'bg-success-50',
      icon: 'text-success-600',
      trend: trend === 'up' ? 'text-success-600' : 'text-error-600'
    },
    info: {
      bg: 'bg-info-50',
      icon: 'text-info-600',
      trend: trend === 'up' ? 'text-success-600' : 'text-error-600'
    },
    warning: {
      bg: 'bg-warning-50',
      icon: 'text-warning-600',
      trend: trend === 'up' ? 'text-success-600' : 'text-error-600'
    },
    error: {
      bg: 'bg-error-50',
      icon: 'text-error-600',
      trend: trend === 'up' ? 'text-success-600' : 'text-error-600'
    }
  };

  const classes = colorClasses[color] || colorClasses.primary;

  return (
    <motion.div
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
      className="card p-6 hover:shadow-medium transition-all duration-200"
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center justify-between mb-4">
            <div className={`p-3 rounded-xl ${classes.bg}`}>
              <Icon className={`h-6 w-6 ${classes.icon}`} />
            </div>
            <div className={`flex items-center text-sm font-medium ${classes.trend}`}>
              {trend === 'up' ? (
                <TrendingUp className="h-4 w-4 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 mr-1" />
              )}
              {change}
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-600 mb-1">{title}</h3>
            <p className="text-2xl font-bold text-gray-900 mb-1">{value}</p>
            {description && (
              <p className="text-xs text-gray-500">{description}</p>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default StatsCard;
