import React from 'react';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import { Eye, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';

import { analyticsAPI } from '../../services/api';
import { useAuthStore } from '../../store/authStore';
import LoadingSpinner from '../UI/LoadingSpinner';

const RecentOrders = () => {
  const { getToken } = useAuthStore();

  const { data: ordersResponse, isLoading } = useQuery(
    'recentOrders',
    () => analyticsAPI.getRecentOrders(5, getToken()),
    {
      refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
    }
  );

  const orders = ordersResponse?.data?.recentOrders || [];

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-success-600" />;
      case 'processing':
        return <Clock className="h-4 w-4 text-warning-600" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-error-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-info-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-success-100 text-success-800';
      case 'processing':
        return 'bg-warning-100 text-warning-800';
      case 'cancelled':
        return 'bg-error-100 text-error-800';
      default:
        return 'bg-info-100 text-info-800';
    }
  };

  return (
    <div className="card p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">Recent Orders</h3>
          <p className="text-sm text-gray-600">Latest customer orders</p>
        </div>
        <a
          href="/orders"
          className="text-sm font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200"
        >
          View all
        </a>
      </div>

      {/* Orders List */}
      {isLoading ? (
        <div className="flex items-center justify-center h-48">
          <LoadingSpinner size="lg" />
        </div>
      ) : orders.length === 0 ? (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full mb-4">
            <Eye className="h-6 w-6 text-gray-400" />
          </div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">No orders yet</h4>
          <p className="text-sm text-gray-600">Orders will appear here once customers start purchasing.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {orders.map((order, index) => (
            <motion.div
              key={order.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {getStatusIcon(order.status)}
                </div>
                <div>
                  <div className="flex items-center space-x-2 mb-1">
                    <h4 className="text-sm font-medium text-gray-900">
                      #{order.orderNumber}
                    </h4>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">
                    {order.customer?.name || 'Guest Customer'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {format(new Date(order.createdAt), 'MMM dd, yyyy HH:mm')}
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">
                  ${parseFloat(order.totalAmount).toFixed(2)}
                </p>
                <p className="text-xs text-gray-500">
                  {order.paymentStatus === 'paid' ? 'Paid' : 'Pending'}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Footer */}
      {orders.length > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              Showing {orders.length} recent orders
            </span>
            <a
              href="/orders"
              className="font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200"
            >
              View all orders →
            </a>
          </div>
        </div>
      )}
    </div>
  );
};

export default RecentOrders;
