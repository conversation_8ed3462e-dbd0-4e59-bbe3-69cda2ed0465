import React from 'react';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import { Package, TrendingUp, Eye } from 'lucide-react';

import { analyticsAPI } from '../../services/api';
import { useAuthStore } from '../../store/authStore';
import LoadingSpinner from '../UI/LoadingSpinner';

const TopProducts = () => {
  const { getToken } = useAuthStore();

  const { data: productsResponse, isLoading } = useQuery(
    'topProducts',
    () => analyticsAPI.getTopProducts(5, getToken()),
    {
      refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
    }
  );

  const products = productsResponse?.data?.topProducts || [];

  const getProductImage = (product) => {
    if (product.images && product.images.length > 0) {
      const mainImage = product.images.find(img => img.isMain) || product.images[0];
      return mainImage.url;
    }
    return '/placeholder-product.jpg'; // You'll need to add this placeholder image
  };

  return (
    <div className="card p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">Top Products</h3>
          <p className="text-sm text-gray-600">Best selling products</p>
        </div>
        <a
          href="/products"
          className="text-sm font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200"
        >
          View all
        </a>
      </div>

      {/* Products List */}
      {isLoading ? (
        <div className="flex items-center justify-center h-48">
          <LoadingSpinner size="lg" />
        </div>
      ) : products.length === 0 ? (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full mb-4">
            <Package className="h-6 w-6 text-gray-400" />
          </div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">No products yet</h4>
          <p className="text-sm text-gray-600">Add products to see sales data here.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {products.map((product, index) => (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              {/* Rank */}
              <div className="flex-shrink-0">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                  index === 0 ? 'bg-yellow-100 text-yellow-800' :
                  index === 1 ? 'bg-gray-100 text-gray-800' :
                  index === 2 ? 'bg-orange-100 text-orange-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  {index + 1}
                </div>
              </div>

              {/* Product Image */}
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-gray-200 rounded-lg overflow-hidden">
                  <img
                    src={getProductImage(product)}
                    alt={product.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzNkMzMC42Mjc0IDM2IDM2IDMwLjYyNzQgMzYgMjRDMzYgMTcuMzcyNiAzMC42Mjc0IDEyIDI0IDEyQzE3LjM3MjYgMTIgMTIgMTcuMzcyNiAxMiAyNEMxMiAzMC42Mjc0IDE3LjM3MjYgMzYgMjQgMzZaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0yNCAyOEMyNi4yMDkxIDI4IDI4IDI2LjIwOTEgMjggMjRDMjggMjEuNzkwOSAyNi4yMDkxIDIwIDI0IDIwQzIxLjc5MDkgMjAgMjAgMjEuNzkwOSAyMCAyNEMyMCAyNi4yMDkxIDIxLjc5MDkgMjggMjQgMjhaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=';
                    }}
                  />
                </div>
              </div>

              {/* Product Info */}
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 truncate">
                  {product.name}
                </h4>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-sm font-medium text-primary-600">
                    ${parseFloat(product.price).toFixed(2)}
                  </span>
                  <span className="text-xs text-gray-500">
                    • {product.salesCount || 0} sold
                  </span>
                </div>
              </div>

              {/* Sales Indicator */}
              <div className="flex-shrink-0">
                <div className="flex items-center space-x-1">
                  <TrendingUp className="h-4 w-4 text-success-600" />
                  <span className="text-xs font-medium text-success-600">
                    {product.salesCount || 0}
                  </span>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Footer */}
      {products.length > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              Top {products.length} products by sales
            </span>
            <a
              href="/products"
              className="font-medium text-primary-600 hover:text-primary-700 transition-colors duration-200"
            >
              Manage products →
            </a>
          </div>
        </div>
      )}
    </div>
  );
};

export default TopProducts;
