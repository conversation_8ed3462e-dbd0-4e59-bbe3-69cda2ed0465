import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Upload, 
  X, 
  Play, 
  Video,
  AlertCircle,
  FileVideo,
  Trash2
} from 'lucide-react';
import toast from 'react-hot-toast';

const VideoUploader = ({ video, onVideoChange, onVideoRemove }) => {
  const fileInputRef = useRef(null);
  const [dragOver, setDragOver] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [videoPreview, setVideoPreview] = useState(null);

  const handleFileSelect = (files) => {
    const file = files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('video/')) {
      toast.error('Please select a valid video file');
      return;
    }

    // Validate file size (50MB limit)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      toast.error('Video file is too large. Maximum size is 50MB');
      return;
    }

    setUploading(true);

    // Create video preview
    const reader = new FileReader();
    reader.onload = (e) => {
      const videoData = {
        file,
        url: e.target.result,
        name: file.name,
        size: file.size,
        type: file.type
      };
      
      setVideoPreview(videoData);
      onVideoChange(videoData);
      setUploading(false);
      toast.success('Video uploaded successfully');
    };
    
    reader.readAsDataURL(file);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    const files = e.dataTransfer.files;
    handleFileSelect(files);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const removeVideo = () => {
    setVideoPreview(null);
    onVideoRemove();
    toast.success('Video removed');
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (duration) => {
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const currentVideo = video || videoPreview;

  return (
    <div className="space-y-4">
      {!currentVideo ? (
        // Upload Area
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            dragOver 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
          } ${uploading ? 'opacity-50 pointer-events-none' : ''}`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="video/*"
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
          />
          
          <div className="space-y-2">
            {uploading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <Video className="mx-auto h-12 w-12 text-gray-400" />
            )}
            
            <div>
              <p className="text-lg font-medium text-gray-900">
                {uploading ? 'Uploading video...' : 'Upload product video'}
              </p>
              <p className="text-sm text-gray-500">
                Drag and drop a video here, or click to select
              </p>
              <p className="text-xs text-gray-400 mt-1">
                MP4, MOV, AVI up to 50MB
              </p>
            </div>
          </div>
        </div>
      ) : (
        // Video Preview
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white border border-gray-200 rounded-lg overflow-hidden"
        >
          <div className="relative">
            {/* Video Player */}
            <div className="aspect-video bg-gray-900 flex items-center justify-center">
              <video
                src={currentVideo.url}
                controls
                className="w-full h-full object-contain"
                preload="metadata"
              >
                Your browser does not support the video tag.
              </video>
            </div>
            
            {/* Remove Button */}
            <button
              onClick={removeVideo}
              className="absolute top-2 right-2 p-2 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors shadow-lg"
              title="Remove video"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          
          {/* Video Info */}
          <div className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileVideo className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 truncate max-w-xs">
                    {currentVideo.name}
                  </h4>
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                    <span>{formatFileSize(currentVideo.size)}</span>
                    <span className="capitalize">{currentVideo.type?.split('/')[1]}</span>
                  </div>
                </div>
              </div>
              
              <button
                onClick={removeVideo}
                className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                title="Remove video"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        </motion.div>
      )}
      
      {/* Video Guidelines */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
          <div className="text-sm">
            <h4 className="font-medium text-blue-900 mb-1">Video Guidelines</h4>
            <ul className="text-blue-800 space-y-1">
              <li>• Maximum file size: 50MB</li>
              <li>• Supported formats: MP4, MOV, AVI, WebM</li>
              <li>• Recommended resolution: 1080p or higher</li>
              <li>• Keep videos under 2 minutes for better engagement</li>
              <li>• Ensure good lighting and clear audio</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoUploader;
