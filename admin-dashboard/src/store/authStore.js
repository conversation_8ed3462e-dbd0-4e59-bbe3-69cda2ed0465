import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authAPI } from '../services/api';

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions

      login: async (email, password) => {
        try {
          console.log('🔄 Starting login process...');
          set({ isLoading: true, error: null });

          const response = await authAPI.login(email, password);
          console.log('✅ Login API response received:', response);

          // Handle different response structures
          let user, token;
          if (response.data && response.data.user) {
            user = response.data.user;
            token = response.token || response.data.token;
          } else if (response.user) {
            user = response.user;
            token = response.token;
          } else {
            throw new Error('Invalid response structure');
          }

          console.log('Login data:', { hasUser: !!user, hasToken: !!token, userRole: user?.role });

          // Check if user is admin
          if (user.role !== 'admin') {
            throw new Error('Access denied. Admin privileges required.');
          }

          console.log('✅ Admin user verified, updating store...');

          // Update store state
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          // Save to localStorage with expiration (30 days)
          const expirationTime = Date.now() + (30 * 24 * 60 * 60 * 1000); // 30 days
          const storeData = {
            state: {
              user,
              token,
              isAuthenticated: true,
              expiresAt: expirationTime
            }
          };
          localStorage.setItem('admin-auth-storage', JSON.stringify(storeData));
          console.log('✅ Auth data saved to localStorage with 30-day expiration');

          // Dispatch custom event for login success
          window.dispatchEvent(new CustomEvent('loginSuccess', {
            detail: { user, token }
          }));

          console.log('✅ Login process completed successfully');
          return { success: true };
        } catch (error) {
          console.error('Login error details:', error);

          let errorMessage = 'Login failed';

          if (error.response?.data?.message) {
            errorMessage = error.response.data.message;
          } else if (error.response?.data?.error?.message) {
            errorMessage = error.response.data.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          set({
            isLoading: false,
            error: errorMessage,
          });
          return { success: false, error: errorMessage };
        }
      },

      logout: async () => {
        try {
          console.log('🔄 Starting logout process...');
          const token = get().token;

          if (token) {
            try {
              await authAPI.logout(token);
              console.log('✅ Server logout successful');
            } catch (error) {
              console.log('⚠️ Server logout failed, continuing with local logout');
            }
          }
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          // Clear localStorage
          localStorage.removeItem('admin-auth-storage');
          console.log('✅ Local storage cleared');

          // Clear state
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });

          console.log('✅ Logout completed successfully');
        }
      },

      updateProfile: async (userData) => {
        try {
          set({ isLoading: true, error: null });
          
          const token = get().token;
          const response = await authAPI.updateProfile(userData, token);
          
          set({
            user: response.data.user,
            isLoading: false,
            error: null,
          });
          
          return { success: true };
        } catch (error) {
          const errorMessage = error.response?.data?.message || 'Profile update failed';
          set({
            isLoading: false,
            error: errorMessage,
          });
          return { success: false, error: errorMessage };
        }
      },

      clearError: () => {
        set({ error: null });
      },

      // Initialize auth from localStorage
      initialize: () => {
        console.log('🔄 Initializing auth store...');

        try {
          // Check current state first
          const currentState = get();
          console.log('Current auth state:', {
            isAuthenticated: currentState.isAuthenticated,
            hasToken: !!currentState.token,
            hasUser: !!currentState.user
          });

          // If already authenticated, don't reinitialize
          if (currentState.isAuthenticated && currentState.token && currentState.user) {
            console.log('✅ Already authenticated, skipping initialization');
            set({ isLoading: false });
            return;
          }

          const stored = localStorage.getItem('admin-auth-storage');
          console.log('Stored data exists:', !!stored);

          if (stored) {
            const parsedData = JSON.parse(stored);
            console.log('Parsed data structure:', Object.keys(parsedData));

            // Handle both old and new storage formats
            let token, user, expiresAt;

            if (parsedData.state) {
              // New format with state wrapper
              token = parsedData.state.token;
              user = parsedData.state.user;
              expiresAt = parsedData.state.expiresAt;
            } else {
              // Direct format (old)
              token = parsedData.token;
              user = parsedData.user;
              expiresAt = null; // Old format doesn't have expiration
            }

            console.log('Extracted data:', { hasToken: !!token, hasUser: !!user, expiresAt });

            // Check if session has expired
            if (expiresAt && Date.now() > expiresAt) {
              console.log('❌ Session expired, clearing...');
              localStorage.removeItem('admin-auth-storage');
            } else if (token && user && user.role === 'admin') {
              console.log('✅ Valid stored auth data found, restoring session');

              // If no expiration set (old format), add 30 days from now
              if (!expiresAt) {
                const newExpirationTime = Date.now() + (30 * 24 * 60 * 60 * 1000);
                const updatedStoreData = {
                  state: {
                    user,
                    token,
                    isAuthenticated: true,
                    expiresAt: newExpirationTime
                  }
                };
                localStorage.setItem('admin-auth-storage', JSON.stringify(updatedStoreData));
                console.log('✅ Updated old session with 30-day expiration');
              }

              set({
                user,
                token,
                isAuthenticated: true,
                isLoading: false,
                error: null,
              });
              return;
            } else {
              console.log('❌ Invalid stored data, clearing...');
              localStorage.removeItem('admin-auth-storage');
            }
          }
        } catch (error) {
          console.error('❌ Error initializing auth:', error);
          localStorage.removeItem('admin-auth-storage');
        }

        // If no valid stored data, set as not authenticated
        console.log('🔄 No valid auth data, setting as unauthenticated');
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },

      // Getters
      getToken: () => get().token,
      getUser: () => get().user,
      isLoggedIn: () => get().isAuthenticated,
    }),
    {
      name: 'admin-auth-storage',
      partialize: (state) => ({ 
        token: state.token,
        user: state.user,
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
);

export { useAuthStore };
