@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&family=Cairo:wght@300;400;500;600;700;800&display=swap');

/* RTL support and theme variables */
@layer base {
  :root {
    --color-primary: 59 130 246;
    --color-primary-dark: 37 99 235;
    --color-secondary: 156 163 175;
    --color-success: 34 197 94;
    --color-warning: 251 191 36;
    --color-error: 239 68 68;
    --color-info: 59 130 246;

    /* Light theme colors only */
    --color-bg-primary: 255 255 255;
    --color-bg-secondary: 249 250 251;
    --color-bg-tertiary: 243 244 246;
    --color-text-primary: 17 24 39;
    --color-text-secondary: 75 85 99;
    --color-text-tertiary: 156 163 175;
    --color-border: 229 231 235;
    --color-border-light: 243 244 246;
  }

  body {
    @apply bg-gray-50 text-gray-900 transition-colors duration-200;
  }

  /* RTL support */
  [dir="rtl"] {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  [dir="ltr"] {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }

  /* RTL specific styles */
  [dir="rtl"] .sidebar {
    right: 0;
    left: auto;
  }

  [dir="rtl"] .sidebar-overlay {
    z-index: 40;
  }

  [dir="rtl"] .main-content {
    margin-right: 16rem;
    margin-left: 0;
  }

  [dir="rtl"] .lg\:mr-64 {
    margin-right: 16rem;
    margin-left: 0;
  }

  /* RTL mobile sidebar */
  [dir="rtl"] .mobile-sidebar {
    right: 0;
    left: auto;
    transform: translateX(100%);
  }

  [dir="rtl"] .mobile-sidebar.open {
    transform: translateX(0);
  }
}

/* Base Styles */
@layer base {
  * {
    @apply border-gray-200;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

/* Component Styles */
@layer components {
  /* Custom Scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full hover:bg-gray-400;
  }
  
  /* Glass Effect */
  .glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
  }
  
  /* Gradient Backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
  }
  
  .gradient-secondary {
    background: linear-gradient(135deg, #64748b 0%, #334155 100%);
  }
  
  .gradient-success {
    background: linear-gradient(135deg, #22c55e 0%, #15803d 100%);
  }
  
  .gradient-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  }
  
  .gradient-error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  }
  
  .gradient-info {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }
  
  /* Card Styles */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-200/50;
  }
  
  .card-hover {
    @apply card transition-all duration-300 hover:shadow-medium hover:-translate-y-1;
  }
  
  /* Button Styles */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-error {
    @apply btn bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }
  
  .btn-outline {
    @apply btn border-2 border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
  }
  
  .btn-ghost {
    @apply btn text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
  }
  
  /* Input Styles */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .input-error {
    @apply input border-error-300 focus:ring-error-500 focus:border-error-500;
  }
  
  /* Badge Styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply badge bg-gray-100 text-gray-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-error {
    @apply badge bg-error-100 text-error-800;
  }
  
  .badge-info {
    @apply badge bg-info-100 text-info-800;
  }
  
  /* Loading Animation */
  .loading {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* Sidebar Styles */
  .sidebar-item {
    @apply flex items-center px-4 py-3 text-gray-700 rounded-lg transition-all duration-200 hover:bg-gray-100 hover:text-primary-600;
  }
  
  .sidebar-item-active {
    @apply sidebar-item bg-primary-50 text-primary-600 border-r-2 border-primary-600;
  }
  
  /* Table Styles */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  /* Modal Styles */
  .modal-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
  }
  
  .modal-content {
    @apply bg-white rounded-xl shadow-large max-w-md w-full max-h-screen overflow-y-auto;
  }
  
  /* Dropdown Styles */
  .dropdown {
    @apply absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-large border border-gray-200 py-1 z-50;
  }
  
  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150;
  }
}

/* Utility Classes */
@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .border-gradient {
    border-image: linear-gradient(135deg, #ec4899 0%, #be185d 100%) 1;
  }
  
  .shadow-colored {
    box-shadow: 0 10px 25px -5px rgba(236, 72, 153, 0.25), 0 10px 10px -5px rgba(236, 72, 153, 0.04);
  }
}
