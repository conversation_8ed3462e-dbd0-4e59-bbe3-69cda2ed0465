import React from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from 'react-query';
import { ordersAPI } from '../../services/api';
import { useAuthStore } from '../../store/authStore';
import LoadingSpinner from '../../components/UI/LoadingSpinner';

const OrderDetailPage = () => {
  const { id } = useParams();
  const { getToken } = useAuthStore();

  const { data: orderResponse, isLoading } = useQuery(
    ['order', id],
    () => ordersAPI.getOrder(id, getToken())
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  const order = orderResponse?.data?.order;

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Order #{order?.orderNumber}</h1>
        <p className="text-gray-600">Order details and management</p>
      </div>
      
      <div className="card p-6">
        <p className="text-gray-600">Order detail page content will be implemented here.</p>
      </div>
    </div>
  );
};

export default OrderDetailPage;
