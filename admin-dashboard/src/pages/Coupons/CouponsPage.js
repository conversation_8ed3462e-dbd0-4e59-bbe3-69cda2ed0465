import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import { Plus, Percent, Edit, Trash2, Copy } from 'lucide-react';
import { format } from 'date-fns';

import { couponsAPI } from '../../services/api';
import { useAuthStore } from '../../store/authStore';
import LoadingSpinner from '../../components/UI/LoadingSpinner';

const CouponsPage = () => {
  const { getToken } = useAuthStore();

  const { data: couponsResponse, isLoading } = useQuery(
    'coupons',
    () => couponsAPI.getCoupons({}, getToken())
  );

  const coupons = couponsResponse?.data?.coupons || [];

  const getTypeColor = (type) => {
    switch (type) {
      case 'percentage':
        return 'bg-success-100 text-success-800';
      case 'fixed':
        return 'bg-info-100 text-info-800';
      case 'free_shipping':
        return 'bg-warning-100 text-warning-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Coupons</h1>
            <p className="text-gray-600">Create and manage discount coupons</p>
          </div>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="h-5 w-5" />
            <span>Add Coupon</span>
          </motion.button>
        </div>
      </div>

      <div className="card overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        ) : coupons.length === 0 ? (
          <div className="text-center py-12">
            <Percent className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No coupons yet</h3>
            <p className="text-gray-600 mb-6">Create discount coupons to boost sales</p>
            <button className="btn-primary">Create Your First Coupon</button>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="table">
              <thead className="table-header">
                <tr>
                  <th className="table-header-cell">Code</th>
                  <th className="table-header-cell">Type</th>
                  <th className="table-header-cell">Value</th>
                  <th className="table-header-cell">Usage</th>
                  <th className="table-header-cell">Valid Until</th>
                  <th className="table-header-cell">Status</th>
                  <th className="table-header-cell">Actions</th>
                </tr>
              </thead>
              <tbody className="table-body">
                {coupons.map((coupon, index) => (
                  <motion.tr
                    key={coupon.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="hover:bg-gray-50"
                  >
                    <td className="table-cell">
                      <div className="flex items-center space-x-2">
                        <span className="font-mono text-sm font-medium text-gray-900">
                          {coupon.code}
                        </span>
                        <button className="p-1 text-gray-400 hover:text-primary-600">
                          <Copy className="h-3 w-3" />
                        </button>
                      </div>
                    </td>
                    <td className="table-cell">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(coupon.type)}`}>
                        {coupon.type.replace('_', ' ')}
                      </span>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900">
                        {coupon.type === 'percentage' ? `${coupon.value}%` : 
                         coupon.type === 'fixed' ? `$${coupon.value}` : 
                         'Free Shipping'}
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900">
                        {coupon.usedCount || 0}
                        {coupon.usageLimit && ` / ${coupon.usageLimit}`}
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="text-sm text-gray-900">
                        {format(new Date(coupon.endDate), 'MMM dd, yyyy')}
                      </div>
                    </td>
                    <td className="table-cell">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        coupon.active ? 'bg-success-100 text-success-800' : 'bg-error-100 text-error-800'
                      }`}>
                        {coupon.active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center space-x-2">
                        <button className="p-1 text-gray-400 hover:text-warning-600 transition-colors duration-200">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-error-600 transition-colors duration-200">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default CouponsPage;
