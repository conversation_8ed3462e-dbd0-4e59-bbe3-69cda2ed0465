import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Package,
  DollarSign,
  TrendingUp,
  Grid3X3,
  List,
  X,
  Save,
  Video,
  Tag,
  BarChart3,
  Globe,
  Star,
  MoreVertical,
  Image as ImageIcon,
  Upload
} from 'lucide-react';
import toast from 'react-hot-toast';

import { productsAPI } from '../../services/api';
import { useAuthStore } from '../../store/authStore';
import LoadingSpinner from '../../components/UI/LoadingSpinner';
import ImageUploader from '../../components/Products/ImageUploader';
import TagsInput from '../../components/Products/TagsInput';
import VideoUploader from '../../components/Products/VideoUploader';

const ProductsPage = () => {
  const { t, isRTL } = useLanguage();
  const { user } = useAuthStore();

  // View and filter states
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedProducts, setSelectedProducts] = useState([]);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);

  // Data states
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [availableTags, setAvailableTags] = useState([]);

  // Form state
  const [formData, setFormData] = useState({
    // Basic Info
    name: '',
    description: '',
    shortDescription: '',

    // Pricing
    price: '',
    comparePrice: '',
    costPrice: '',

    // Inventory
    sku: '',
    barcode: '',
    quantity: '',
    trackQuantity: true,
    allowBackorder: false,

    // Organization
    category: '',
    brand: '',
    tags: [],

    // Media
    images: [],
    mainImageIndex: 0,
    video: null,

    // SEO
    seoTitle: '',
    seoDescription: '',
    seoKeywords: '',
    slug: '',

    // Status
    status: 'active',
    featured: false,

    // Shipping
    weight: '',
    dimensions: {
      length: '',
      width: '',
      height: ''
    },

    // Variants (for future use)
    hasVariants: false,
    variants: []
  });

  useEffect(() => {
    loadProducts();
    loadAvailableTags();
  }, [currentPage, searchTerm, filterStatus]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        limit: viewMode === 'grid' ? 12 : 10,
        search: searchTerm,
        status: filterStatus !== 'all' ? filterStatus : undefined
      };

      const response = await productsAPI.getProducts(params, user.token);
      setProducts(response.data?.products || []);
      setTotalPages(Math.ceil((response.data?.total || 0) / params.limit));
    } catch (error) {
      console.error('Error loading products:', error);
      toast.error('Failed to load products');
      // Mock data for demo
      setProducts([
        {
          id: 1,
          name: 'iPhone 14 Pro',
          description: 'Latest iPhone with advanced features and Pro camera system',
          shortDescription: 'Latest iPhone with advanced features',
          price: 4999,
          comparePrice: 5499,
          sku: 'IPH14PRO001',
          quantity: 25,
          category: 'Electronics',
          brand: 'Apple',
          status: 'active',
          featured: true,
          images: [
            'https://via.placeholder.com/400x400?text=iPhone+14+Pro+Main',
            'https://via.placeholder.com/400x400?text=iPhone+14+Pro+2',
            'https://via.placeholder.com/400x400?text=iPhone+14+Pro+3'
          ],
          mainImageIndex: 0,
          video: null,
          tags: ['smartphone', 'apple', 'premium', 'camera'],
          rating: 4.8,
          reviews: 156,
          seoTitle: 'iPhone 14 Pro - Advanced Pro Camera System',
          seoDescription: 'Experience the power of iPhone 14 Pro with advanced camera system and A16 Bionic chip.',
          weight: '206g',
          dimensions: { length: '147.5', width: '71.5', height: '7.85' }
        },
        {
          id: 2,
          name: 'Samsung Galaxy S23',
          description: 'Premium Android smartphone with exceptional camera and performance',
          shortDescription: 'Premium Android smartphone',
          price: 3999,
          comparePrice: 4299,
          sku: 'SAM23001',
          quantity: 18,
          category: 'Electronics',
          brand: 'Samsung',
          status: 'active',
          featured: false,
          images: [
            'https://via.placeholder.com/400x400?text=Galaxy+S23+Main',
            'https://via.placeholder.com/400x400?text=Galaxy+S23+2'
          ],
          mainImageIndex: 0,
          video: null,
          tags: ['smartphone', 'samsung', 'android', 'camera'],
          rating: 4.6,
          reviews: 89,
          seoTitle: 'Samsung Galaxy S23 - Premium Android Experience',
          seoDescription: 'Discover the Samsung Galaxy S23 with advanced camera technology and powerful performance.',
          weight: '168g',
          dimensions: { length: '146.3', width: '70.9', height: '7.6' }
        }
      ]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableTags = async () => {
    try {
      const response = await productsAPI.getProductTags(user.token);
      setAvailableTags(response.data || []);
    } catch (error) {
      // Mock tags for demo
      setAvailableTags([
        'smartphone', 'laptop', 'tablet', 'accessories', 'electronics',
        'apple', 'samsung', 'premium', 'budget', 'gaming', 'camera',
        'wireless', 'bluetooth', 'fast-charging', 'waterproof'
      ]);
    }
  };

  // CRUD Operations
  const handleCreate = async () => {
    try {
      if (!formData.name.trim()) {
        toast.error('Product name is required');
        return;
      }

      if (formData.images.length === 0) {
        toast.error('At least one image is required');
        return;
      }

      const productData = {
        ...formData,
        slug: formData.slug || generateSlug(formData.name)
      };

      const response = await productsAPI.createProduct(productData, user.token);
      if (response.success) {
        toast.success('Product created successfully!');
        setShowCreateModal(false);
        resetForm();
        loadProducts();
      }
    } catch (error) {
      toast.error('Failed to create product');
      console.error('Create error:', error);
    }
  };

  const handleEdit = async () => {
    try {
      const productData = {
        ...formData,
        slug: formData.slug || generateSlug(formData.name)
      };

      const response = await productsAPI.updateProduct(editingProduct.id, productData, user.token);
      if (response.success) {
        toast.success('Product updated successfully!');
        setShowEditModal(false);
        setEditingProduct(null);
        resetForm();
        loadProducts();
      }
    } catch (error) {
      toast.error('Failed to update product');
      console.error('Update error:', error);
    }
  };

  const handleDelete = async (productId) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await productsAPI.deleteProduct(productId, user.token);
        toast.success('Product deleted successfully!');
        loadProducts();
      } catch (error) {
        toast.error('Failed to delete product');
        console.error('Delete error:', error);
      }
    }
  };

  const handleBulkDelete = async () => {
    if (selectedProducts.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedProducts.length} products?`)) {
      try {
        await productsAPI.bulkDeleteProducts(selectedProducts, user.token);
        toast.success(`${selectedProducts.length} products deleted successfully!`);
        setSelectedProducts([]);
        loadProducts();
      } catch (error) {
        toast.error('Failed to delete products');
        console.error('Bulk delete error:', error);
      }
    }
  };

  // Helper Functions
  const generateSlug = (name) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      shortDescription: '',
      price: '',
      comparePrice: '',
      costPrice: '',
      sku: '',
      barcode: '',
      quantity: '',
      trackQuantity: true,
      allowBackorder: false,
      category: '',
      brand: '',
      tags: [],
      images: [],
      mainImageIndex: 0,
      video: null,
      seoTitle: '',
      seoDescription: '',
      seoKeywords: '',
      slug: '',
      status: 'active',
      featured: false,
      weight: '',
      dimensions: { length: '', width: '', height: '' },
      hasVariants: false,
      variants: []
    });
  };

  const openEditModal = (product) => {
    setEditingProduct(product);
    setFormData({
      name: product.name || '',
      description: product.description || '',
      shortDescription: product.shortDescription || '',
      price: product.price || '',
      comparePrice: product.comparePrice || '',
      costPrice: product.costPrice || '',
      sku: product.sku || '',
      barcode: product.barcode || '',
      quantity: product.quantity || '',
      trackQuantity: product.trackQuantity !== false,
      allowBackorder: product.allowBackorder || false,
      category: product.category || '',
      brand: product.brand || '',
      tags: product.tags || [],
      images: product.images || [],
      mainImageIndex: product.mainImageIndex || 0,
      video: product.video || null,
      seoTitle: product.seoTitle || '',
      seoDescription: product.seoDescription || '',
      seoKeywords: product.seoKeywords || '',
      slug: product.slug || '',
      status: product.status || 'active',
      featured: product.featured || false,
      weight: product.weight || '',
      dimensions: product.dimensions || { length: '', width: '', height: '' },
      hasVariants: product.hasVariants || false,
      variants: product.variants || []
    });
    setShowEditModal(true);
  };

  const toggleProductSelection = (productId) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const selectAllProducts = () => {
    if (selectedProducts.length === products.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(products.map(p => p.id));
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleFilterChange = (status) => {
    setFilterStatus(status);
    setCurrentPage(1);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`p-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{t('products')}</h1>
            <p className="mt-2 text-gray-600">Manage your store products</p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Product
          </button>
        </div>
      </div>

      {/* Filters and View Controls */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={handleSearch}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* View Mode Toggle */}
          <div className="flex items-center gap-2">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Grid View"
              >
                <Grid3X3 className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="List View"
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Status Filter */}
          <div className="flex gap-2">
            <select
              value={filterStatus}
              onChange={(e) => handleFilterChange(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="draft">Draft</option>
              <option value="inactive">Inactive</option>
            </select>

            <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center">
              <Filter className="w-4 h-4 mr-2" />
              More Filters
            </button>

            {selectedProducts.length > 0 && (
              <button
                onClick={handleBulkDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete ({selectedProducts.length})
              </button>
            )}
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedProducts.length > 0 && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-800">
                {selectedProducts.length} product(s) selected
              </span>
              <div className="flex gap-2">
                <button
                  onClick={() => setSelectedProducts([])}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  Clear selection
                </button>
                <button
                  onClick={selectAllProducts}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  {selectedProducts.length === products.length ? 'Deselect all' : 'Select all'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Products Table */}
      <div className="card overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-12">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-600 mb-6">
              {searchTerm ? 'Try adjusting your search terms' : 'Get started by adding your first product'}
            </p>
            <a href="/products/new" className="btn-primary">
              Add Product
            </a>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr>
                    <th className="table-header-cell">Product</th>
                    <th className="table-header-cell">Price</th>
                    <th className="table-header-cell">Stock</th>
                    <th className="table-header-cell">Status</th>
                    <th className="table-header-cell">Sales</th>
                    <th className="table-header-cell">Actions</th>
                  </tr>
                </thead>
                <tbody className="table-body">
                  {products.map((product, index) => (
                    <motion.tr
                      key={product.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="hover:bg-gray-50"
                    >
                      <td className="table-cell">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0 h-12 w-12">
                            <img
                              className="h-12 w-12 rounded-lg object-cover"
                              src={product.images?.[0]?.url || '/placeholder-product.jpg'}
                              alt={product.name}
                              onError={(e) => {
                                e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzNkMzMC42Mjc0IDM2IDM2IDMwLjYyNzQgMzYgMjRDMzYgMTcuMzcyNiAzMC42Mjc0IDEyIDI0IDEyQzE3LjM3MjYgMTIgMTIgMTcuMzcyNiAxMiAyNEMxMiAzMC42Mjc0IDE3LjM3MjYgMzYgMjQgMzZaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik0yNCAyOEMyNi4yMDkxIDI4IDI4IDI2LjIwOTEgMjggMjRDMjggMjEuNzkwOSAyNi4yMDkxIDIwIDI0IDIwQzIxLjc5MDkgMjAgMjAgMjEuNzkwOSAyMCAyNEMyMCAyNi4yMDkxIDIxLjc5MDkgMjggMjQgMjhaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=';
                              }}
                            />
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {product.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {product.category?.name || 'No category'}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="text-sm font-medium text-gray-900">
                          ${parseFloat(product.price).toFixed(2)}
                        </div>
                        {product.comparePrice && (
                          <div className="text-sm text-gray-500 line-through">
                            ${parseFloat(product.comparePrice).toFixed(2)}
                          </div>
                        )}
                      </td>
                      <td className="table-cell">
                        <div className="text-sm text-gray-900">
                          {product.inventory?.quantity || 0}
                        </div>
                      </td>
                      <td className="table-cell">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(product.status)}`}>
                          {product.status}
                        </span>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center text-sm text-gray-900">
                          <TrendingUp className="h-4 w-4 text-success-600 mr-1" />
                          {product.salesCount || 0}
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          <button className="p-1 text-gray-400 hover:text-info-600 transition-colors duration-200">
                            <Eye className="h-4 w-4" />
                          </button>
                          <a
                            href={`/products/${product.id}/edit`}
                            className="p-1 text-gray-400 hover:text-warning-600 transition-colors duration-200"
                          >
                            <Edit className="h-4 w-4" />
                          </a>
                          <button className="p-1 text-gray-400 hover:text-error-600 transition-colors duration-200">
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Showing page {currentPage} of {totalPages}
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ProductsPage;
