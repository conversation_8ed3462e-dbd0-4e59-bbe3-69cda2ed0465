import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import toast from 'react-hot-toast';
import { 
  Save, 
  ArrowLeft, 
  Upload, 
  X,
  Package,
  DollarSign,
  Tag,
  FileText
} from 'lucide-react';

import { productsAPI, categoriesAPI } from '../../services/api';
import { useAuthStore } from '../../store/authStore';
import LoadingSpinner from '../../components/UI/LoadingSpinner';

// Validation schema
const schema = yup.object({
  name: yup.string().required('Product name is required').min(2, 'Name must be at least 2 characters'),
  description: yup.string().required('Description is required').min(10, 'Description must be at least 10 characters'),
  shortDescription: yup.string().max(200, 'Short description must be less than 200 characters'),
  price: yup.number().required('Price is required').min(0, 'Price must be positive'),
  comparePrice: yup.number().min(0, 'Compare price must be positive'),
  categoryId: yup.number().required('Category is required'),
  status: yup.string().required('Status is required'),
});

const ProductFormPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEdit = Boolean(id);
  const { getToken } = useAuthStore();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch categories
  const { data: categoriesResponse } = useQuery(
    'categories',
    () => categoriesAPI.getCategories(getToken())
  );

  // Fetch product if editing
  const { data: productResponse, isLoading } = useQuery(
    ['product', id],
    () => productsAPI.getProduct(id, getToken()),
    {
      enabled: isEdit,
    }
  );

  const categories = categoriesResponse?.data?.categories || [];
  const product = productResponse?.data?.product;

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      name: '',
      description: '',
      shortDescription: '',
      price: '',
      comparePrice: '',
      categoryId: '',
      status: 'draft',
      featured: false,
      tags: '',
      inventory: {
        quantity: 0,
        trackQuantity: true,
        allowBackorder: false
      }
    }
  });

  // Set form values when product data is loaded
  React.useEffect(() => {
    if (product) {
      setValue('name', product.name);
      setValue('description', product.description);
      setValue('shortDescription', product.shortDescription || '');
      setValue('price', product.price);
      setValue('comparePrice', product.comparePrice || '');
      setValue('categoryId', product.categoryId);
      setValue('status', product.status);
      setValue('featured', product.featured);
      setValue('tags', product.tags?.join(', ') || '');
      setValue('inventory', product.inventory || {});
    }
  }, [product, setValue]);

  const onSubmit = async (data) => {
    try {
      setIsSubmitting(true);

      // Process tags
      const tags = data.tags ? data.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [];

      const productData = {
        ...data,
        tags,
        price: parseFloat(data.price),
        comparePrice: data.comparePrice ? parseFloat(data.comparePrice) : null,
      };

      if (isEdit) {
        await productsAPI.updateProduct(id, productData, getToken());
        toast.success('Product updated successfully!');
      } else {
        await productsAPI.createProduct(productData, getToken());
        toast.success('Product created successfully!');
      }

      navigate('/products');
    } catch (error) {
      toast.error(error.response?.data?.message || 'Something went wrong');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isEdit && isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-4 mb-4">
          <button
            onClick={() => navigate('/products')}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors duration-200"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isEdit ? 'Edit Product' : 'Add New Product'}
            </h1>
            <p className="text-gray-600">
              {isEdit ? 'Update product information' : 'Create a new product for your store'}
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <div className="card p-6">
              <div className="flex items-center space-x-2 mb-6">
                <Package className="h-5 w-5 text-primary-600" />
                <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Product Name *
                  </label>
                  <input
                    {...register('name')}
                    type="text"
                    className={`input ${errors.name ? 'input-error' : ''}`}
                    placeholder="Enter product name"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-error-600">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Short Description
                  </label>
                  <input
                    {...register('shortDescription')}
                    type="text"
                    className={`input ${errors.shortDescription ? 'input-error' : ''}`}
                    placeholder="Brief product description"
                  />
                  {errors.shortDescription && (
                    <p className="mt-1 text-sm text-error-600">{errors.shortDescription.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    {...register('description')}
                    rows={4}
                    className={`input ${errors.description ? 'input-error' : ''}`}
                    placeholder="Detailed product description"
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-error-600">{errors.description.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tags
                  </label>
                  <input
                    {...register('tags')}
                    type="text"
                    className="input"
                    placeholder="Enter tags separated by commas"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Separate tags with commas (e.g., electronics, smartphone, apple)
                  </p>
                </div>
              </div>
            </div>

            {/* Pricing */}
            <div className="card p-6">
              <div className="flex items-center space-x-2 mb-6">
                <DollarSign className="h-5 w-5 text-primary-600" />
                <h3 className="text-lg font-semibold text-gray-900">Pricing</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Price *
                  </label>
                  <input
                    {...register('price')}
                    type="number"
                    step="0.01"
                    min="0"
                    className={`input ${errors.price ? 'input-error' : ''}`}
                    placeholder="0.00"
                  />
                  {errors.price && (
                    <p className="mt-1 text-sm text-error-600">{errors.price.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Compare Price
                  </label>
                  <input
                    {...register('comparePrice')}
                    type="number"
                    step="0.01"
                    min="0"
                    className={`input ${errors.comparePrice ? 'input-error' : ''}`}
                    placeholder="0.00"
                  />
                  {errors.comparePrice && (
                    <p className="mt-1 text-sm text-error-600">{errors.comparePrice.message}</p>
                  )}
                  <p className="mt-1 text-sm text-gray-500">
                    Original price for discount display
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Status & Visibility */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Status & Visibility</h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status *
                  </label>
                  <select
                    {...register('status')}
                    className={`input ${errors.status ? 'input-error' : ''}`}
                  >
                    <option value="draft">Draft</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                  {errors.status && (
                    <p className="mt-1 text-sm text-error-600">{errors.status.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category *
                  </label>
                  <select
                    {...register('categoryId')}
                    className={`input ${errors.categoryId ? 'input-error' : ''}`}
                  >
                    <option value="">Select a category</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                  {errors.categoryId && (
                    <p className="mt-1 text-sm text-error-600">{errors.categoryId.message}</p>
                  )}
                </div>

                <div className="flex items-center">
                  <input
                    {...register('featured')}
                    type="checkbox"
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label className="ml-2 block text-sm text-gray-900">
                    Featured Product
                  </label>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="card p-6">
              <div className="space-y-4">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full btn-primary flex items-center justify-center space-x-2"
                >
                  {isSubmitting ? (
                    <LoadingSpinner size="sm" />
                  ) : (
                    <>
                      <Save className="h-4 w-4" />
                      <span>{isEdit ? 'Update Product' : 'Create Product'}</span>
                    </>
                  )}
                </motion.button>

                <button
                  type="button"
                  onClick={() => navigate('/products')}
                  className="w-full btn-outline"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ProductFormPage;
