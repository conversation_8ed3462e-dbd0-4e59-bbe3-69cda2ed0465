import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Truck, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  MapPin,
  Clock,
  DollarSign,
  Package,
  Globe,
  Settings,
  Save
} from 'lucide-react';
import toast from 'react-hot-toast';

import { useLanguage } from '../../contexts/LanguageContext';
import { shippingAPI } from '../../services/api';
import { useAuthStore } from '../../store/authStore';

const ShippingPage = () => {
  const { t, isRTL } = useLanguage();
  const { user } = useAuthStore();
  
  const [activeTab, setActiveTab] = useState('methods');
  const [shippingMethods, setShippingMethods] = useState([]);
  const [shippingZones, setShippingZones] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showMethodModal, setShowMethodModal] = useState(false);
  const [showZoneModal, setShowZoneModal] = useState(false);
  const [editingMethod, setEditingMethod] = useState(null);
  const [editingZone, setEditingZone] = useState(null);

  const [methodForm, setMethodForm] = useState({
    name: '',
    description: '',
    type: 'flat_rate',
    price: '',
    freeShippingThreshold: '',
    estimatedDays: '',
    enabled: true
  });

  const [zoneForm, setZoneForm] = useState({
    name: '',
    countries: [],
    cities: [],
    enabled: true
  });

  const tabs = [
    { id: 'methods', label: 'Shipping Methods', icon: Truck },
    { id: 'zones', label: 'Shipping Zones', icon: Globe },
  ];

  useEffect(() => {
    loadData();
  }, [activeTab]);

  const loadData = async () => {
    try {
      setLoading(true);
      if (activeTab === 'methods') {
        await loadShippingMethods();
      } else {
        await loadShippingZones();
      }
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const loadShippingMethods = async () => {
    try {
      const response = await shippingAPI.getMethods(user.token);
      setShippingMethods(response.data || []);
    } catch (error) {
      // Mock data for demo
      setShippingMethods([
        {
          id: 1,
          name: 'Standard Shipping',
          description: 'Regular delivery within Saudi Arabia',
          type: 'flat_rate',
          price: 25,
          freeShippingThreshold: 200,
          estimatedDays: '3-5',
          enabled: true,
          icon: '📦'
        },
        {
          id: 2,
          name: 'Express Shipping',
          description: 'Fast delivery within 24-48 hours',
          type: 'flat_rate',
          price: 50,
          freeShippingThreshold: 500,
          estimatedDays: '1-2',
          enabled: true,
          icon: '⚡'
        },
        {
          id: 3,
          name: 'Same Day Delivery',
          description: 'Delivery within the same day (Riyadh only)',
          type: 'flat_rate',
          price: 75,
          freeShippingThreshold: 1000,
          estimatedDays: '0',
          enabled: false,
          icon: '🚀'
        },
        {
          id: 4,
          name: 'Free Shipping',
          description: 'Free delivery for orders above threshold',
          type: 'free',
          price: 0,
          freeShippingThreshold: 300,
          estimatedDays: '5-7',
          enabled: true,
          icon: '🆓'
        }
      ]);
    }
  };

  const loadShippingZones = async () => {
    try {
      const response = await shippingAPI.getZones(user.token);
      setShippingZones(response.data || []);
    } catch (error) {
      // Mock data for demo
      setShippingZones([
        {
          id: 1,
          name: 'Saudi Arabia - Major Cities',
          countries: ['Saudi Arabia'],
          cities: ['Riyadh', 'Jeddah', 'Dammam', 'Mecca', 'Medina'],
          enabled: true,
          methods: ['Standard Shipping', 'Express Shipping', 'Same Day Delivery']
        },
        {
          id: 2,
          name: 'Saudi Arabia - Other Cities',
          countries: ['Saudi Arabia'],
          cities: ['Abha', 'Tabuk', 'Hail', 'Najran', 'Jazan'],
          enabled: true,
          methods: ['Standard Shipping']
        },
        {
          id: 3,
          name: 'GCC Countries',
          countries: ['UAE', 'Kuwait', 'Bahrain', 'Qatar', 'Oman'],
          cities: [],
          enabled: false,
          methods: ['International Shipping']
        }
      ]);
    }
  };

  const handleCreateMethod = async () => {
    try {
      const response = await shippingAPI.createMethod(methodForm, user.token);
      if (response.success) {
        toast.success('Shipping method created successfully!');
        setShowMethodModal(false);
        resetMethodForm();
        loadShippingMethods();
      }
    } catch (error) {
      toast.error('Failed to create shipping method');
    }
  };

  const handleUpdateMethod = async () => {
    try {
      const response = await shippingAPI.updateMethod(editingMethod.id, methodForm, user.token);
      if (response.success) {
        toast.success('Shipping method updated successfully!');
        setShowMethodModal(false);
        setEditingMethod(null);
        resetMethodForm();
        loadShippingMethods();
      }
    } catch (error) {
      toast.error('Failed to update shipping method');
    }
  };

  const handleDeleteMethod = async (methodId) => {
    if (window.confirm('Are you sure you want to delete this shipping method?')) {
      try {
        await shippingAPI.deleteMethod(methodId, user.token);
        toast.success('Shipping method deleted successfully!');
        loadShippingMethods();
      } catch (error) {
        toast.error('Failed to delete shipping method');
      }
    }
  };

  const handleCreateZone = async () => {
    try {
      const response = await shippingAPI.createZone(zoneForm, user.token);
      if (response.success) {
        toast.success('Shipping zone created successfully!');
        setShowZoneModal(false);
        resetZoneForm();
        loadShippingZones();
      }
    } catch (error) {
      toast.error('Failed to create shipping zone');
    }
  };

  const handleUpdateZone = async () => {
    try {
      const response = await shippingAPI.updateZone(editingZone.id, zoneForm, user.token);
      if (response.success) {
        toast.success('Shipping zone updated successfully!');
        setShowZoneModal(false);
        setEditingZone(null);
        resetZoneForm();
        loadShippingZones();
      }
    } catch (error) {
      toast.error('Failed to update shipping zone');
    }
  };

  const handleDeleteZone = async (zoneId) => {
    if (window.confirm('Are you sure you want to delete this shipping zone?')) {
      try {
        await shippingAPI.deleteZone(zoneId, user.token);
        toast.success('Shipping zone deleted successfully!');
        loadShippingZones();
      } catch (error) {
        toast.error('Failed to delete shipping zone');
      }
    }
  };

  const resetMethodForm = () => {
    setMethodForm({
      name: '',
      description: '',
      type: 'flat_rate',
      price: '',
      freeShippingThreshold: '',
      estimatedDays: '',
      enabled: true
    });
  };

  const resetZoneForm = () => {
    setZoneForm({
      name: '',
      countries: [],
      cities: [],
      enabled: true
    });
  };

  const openEditMethodModal = (method) => {
    setEditingMethod(method);
    setMethodForm({
      name: method.name,
      description: method.description,
      type: method.type,
      price: method.price,
      freeShippingThreshold: method.freeShippingThreshold,
      estimatedDays: method.estimatedDays,
      enabled: method.enabled
    });
    setShowMethodModal(true);
  };

  const openEditZoneModal = (zone) => {
    setEditingZone(zone);
    setZoneForm({
      name: zone.name,
      countries: zone.countries,
      cities: zone.cities,
      enabled: zone.enabled
    });
    setShowZoneModal(true);
  };

  const renderShippingMethods = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Shipping Methods</h2>
        <button
          onClick={() => setShowMethodModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Method
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {shippingMethods.map((method) => (
          <motion.div
            key={method.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <span className="text-2xl mr-3">{method.icon}</span>
                <div>
                  <h3 className="font-semibold text-gray-900">{method.name}</h3>
                  <p className="text-sm text-gray-500">{method.type}</p>
                </div>
              </div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                method.enabled 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {method.enabled ? 'Active' : 'Inactive'}
              </span>
            </div>
            
            <p className="text-gray-600 text-sm mb-4">{method.description}</p>
            
            <div className="space-y-2 mb-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Price:</span>
                <span className="text-sm font-medium">{method.price} SAR</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Free shipping:</span>
                <span className="text-sm font-medium">{method.freeShippingThreshold}+ SAR</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Delivery:</span>
                <span className="text-sm font-medium">{method.estimatedDays} days</span>
              </div>
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => openEditMethodModal(method)}
                className="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
              >
                <Edit className="w-4 h-4 mr-1" />
                Edit
              </button>
              <button
                onClick={() => handleDeleteMethod(method.id)}
                className="bg-red-600 text-white py-2 px-3 rounded-lg hover:bg-red-700 transition-colors"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  const renderShippingZones = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Shipping Zones</h2>
        <button
          onClick={() => setShowZoneModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Zone
        </button>
      </div>

      <div className="space-y-4">
        {shippingZones.map((zone) => (
          <motion.div
            key={zone.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <div className="flex items-start justify-between mb-4">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">{zone.name}</h3>
                <div className="flex items-center text-sm text-gray-600 mb-2">
                  <Globe className="w-4 h-4 mr-1" />
                  Countries: {zone.countries.join(', ')}
                </div>
                {zone.cities.length > 0 && (
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="w-4 h-4 mr-1" />
                    Cities: {zone.cities.slice(0, 3).join(', ')}
                    {zone.cities.length > 3 && ` +${zone.cities.length - 3} more`}
                  </div>
                )}
              </div>
              <div className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  zone.enabled 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {zone.enabled ? 'Active' : 'Inactive'}
                </span>
                <button
                  onClick={() => openEditZoneModal(zone)}
                  className="text-blue-600 hover:text-blue-900"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDeleteZone(zone.id)}
                  className="text-red-600 hover:text-red-900"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            {zone.methods && zone.methods.length > 0 && (
              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">Available Methods:</p>
                <div className="flex flex-wrap gap-2">
                  {zone.methods.map((method, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
                    >
                      {method}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </motion.div>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`p-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Shipping</h1>
        <p className="mt-2 text-gray-600">Manage shipping methods and zones</p>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Icon className="w-5 h-5 mr-2" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'methods' && renderShippingMethods()}
            {activeTab === 'zones' && renderShippingZones()}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ShippingPage;
