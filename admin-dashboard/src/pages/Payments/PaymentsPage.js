import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  CreditCard, 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Download,
  Settings
} from 'lucide-react';
import toast from 'react-hot-toast';

import { useLanguage } from '../../contexts/LanguageContext';
import { paymentsAPI } from '../../services/api';
import { useAuthStore } from '../../store/authStore';

const PaymentsPage = () => {
  const { t, isRTL } = useLanguage();
  const { user } = useAuthStore();
  
  const [activeTab, setActiveTab] = useState('transactions');
  const [transactions, setTransactions] = useState([]);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showMethodModal, setShowMethodModal] = useState(false);
  const [editingMethod, setEditingMethod] = useState(null);

  const tabs = [
    { id: 'transactions', label: 'Transactions', icon: CreditCard },
    { id: 'methods', label: 'Payment Methods', icon: Settings },
  ];

  useEffect(() => {
    loadData();
  }, [activeTab]);

  const loadData = async () => {
    try {
      setLoading(true);
      if (activeTab === 'transactions') {
        await loadTransactions();
      } else {
        await loadPaymentMethods();
      }
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const loadTransactions = async () => {
    try {
      const response = await paymentsAPI.getAll({
        search: searchTerm,
        status: filterStatus !== 'all' ? filterStatus : undefined
      }, user.token);
      setTransactions(response.data || []);
    } catch (error) {
      // Mock data for demo
      setTransactions([
        {
          id: 'TXN001',
          orderId: 'ORD001',
          amount: 299.99,
          currency: 'SAR',
          status: 'completed',
          method: 'Credit Card',
          customer: 'Ahmed Ali',
          date: '2024-01-15T10:30:00Z',
          reference: 'REF123456789'
        },
        {
          id: 'TXN002',
          orderId: 'ORD002',
          amount: 149.50,
          currency: 'SAR',
          status: 'pending',
          method: 'Bank Transfer',
          customer: 'Sara Mohammed',
          date: '2024-01-15T09:15:00Z',
          reference: 'REF987654321'
        },
        {
          id: 'TXN003',
          orderId: 'ORD003',
          amount: 89.99,
          currency: 'SAR',
          status: 'failed',
          method: 'Digital Wallet',
          customer: 'Omar Hassan',
          date: '2024-01-14T16:45:00Z',
          reference: 'REF456789123'
        }
      ]);
    }
  };

  const loadPaymentMethods = async () => {
    try {
      const response = await paymentsAPI.getMethods(user.token);
      setPaymentMethods(response.data || []);
    } catch (error) {
      // Mock data for demo
      setPaymentMethods([
        {
          id: 1,
          name: 'Credit/Debit Cards',
          type: 'card',
          enabled: true,
          description: 'Accept Visa, Mastercard, and other major cards',
          fees: '2.9% + 0.30 SAR',
          icon: '💳'
        },
        {
          id: 2,
          name: 'Bank Transfer',
          type: 'bank_transfer',
          enabled: true,
          description: 'Direct bank transfers',
          fees: '1.5%',
          icon: '🏦'
        },
        {
          id: 3,
          name: 'Digital Wallet',
          type: 'wallet',
          enabled: false,
          description: 'Apple Pay, Google Pay, Samsung Pay',
          fees: '2.5%',
          icon: '📱'
        },
        {
          id: 4,
          name: 'Cash on Delivery',
          type: 'cod',
          enabled: true,
          description: 'Pay when you receive your order',
          fees: 'Free',
          icon: '💵'
        }
      ]);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const togglePaymentMethod = async (methodId, enabled) => {
    try {
      await paymentsAPI.updateMethod(methodId, { enabled }, user.token);
      toast.success(`Payment method ${enabled ? 'enabled' : 'disabled'} successfully`);
      loadPaymentMethods();
    } catch (error) {
      toast.error('Failed to update payment method');
    }
  };

  const processRefund = async (transactionId) => {
    if (window.confirm('Are you sure you want to process a refund for this transaction?')) {
      try {
        await paymentsAPI.processRefund(transactionId, {
          amount: 'full',
          reason: 'Customer request'
        }, user.token);
        toast.success('Refund processed successfully');
        loadTransactions();
      } catch (error) {
        toast.error('Failed to process refund');
      }
    }
  };

  const renderTransactions = () => (
    <div className="space-y-6">
      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
            </select>
            <button className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center">
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Transaction
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Method
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {transactions.map((transaction) => (
                <tr key={transaction.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{transaction.id}</div>
                      <div className="text-sm text-gray-500">Order: {transaction.orderId}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{transaction.customer}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {transaction.amount} {transaction.currency}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{transaction.method}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(transaction.status)}
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                        {transaction.status}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(transaction.date).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <button className="text-blue-600 hover:text-blue-900">
                        <Eye className="w-4 h-4" />
                      </button>
                      {transaction.status === 'completed' && (
                        <button 
                          onClick={() => processRefund(transaction.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <RefreshCw className="w-4 h-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderPaymentMethods = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Payment Methods</h2>
        <button
          onClick={() => setShowMethodModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Method
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {paymentMethods.map((method) => (
          <motion.div
            key={method.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <span className="text-2xl mr-3">{method.icon}</span>
                <div>
                  <h3 className="font-semibold text-gray-900">{method.name}</h3>
                  <p className="text-sm text-gray-500">{method.type}</p>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={method.enabled}
                  onChange={(e) => togglePaymentMethod(method.id, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
            
            <p className="text-gray-600 text-sm mb-4">{method.description}</p>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">Fees: {method.fees}</span>
              <button
                onClick={() => {
                  setEditingMethod(method);
                  setShowMethodModal(true);
                }}
                className="text-blue-600 hover:text-blue-900"
              >
                <Edit className="w-4 h-4" />
              </button>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`p-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Payments</h1>
        <p className="mt-2 text-gray-600">Manage transactions and payment methods</p>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Icon className="w-5 h-5 mr-2" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'transactions' && renderTransactions()}
            {activeTab === 'methods' && renderPaymentMethods()}
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default PaymentsPage;
