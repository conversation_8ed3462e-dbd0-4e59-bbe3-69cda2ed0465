import React from 'react';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Eye,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';

import { analyticsAPI } from '../../services/api';
import { useAuthStore } from '../../store/authStore';

import StatsCard from '../../components/Dashboard/StatsCard';
import SalesChart from '../../components/Dashboard/SalesChart';
import RecentOrders from '../../components/Dashboard/RecentOrders';
import TopProducts from '../../components/Dashboard/TopProducts';
import CustomerStats from '../../components/Dashboard/CustomerStats';

const DashboardPage = () => {
  const { getToken } = useAuthStore();

  // Fetch dashboard stats from API
  const { data: dashboardData, isLoading: statsLoading } = useQuery(
    'dashboardStats',
    () => analyticsAPI.getDashboardStats(getToken()),
    {
      refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    }
  );

  // Fetch recent orders
  const { data: ordersData, isLoading: ordersLoading } = useQuery(
    'recentOrders',
    () => analyticsAPI.getRecentOrders(5, getToken()),
    {
      refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
    }
  );

  // Fetch top products
  const { data: productsData, isLoading: productsLoading } = useQuery(
    'topProducts',
    () => analyticsAPI.getTopProducts(5, getToken()),
    {
      refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
    }
  );

  // Fetch customer stats
  const { data: customerData, isLoading: customerLoading } = useQuery(
    'customerStats',
    () => analyticsAPI.getCustomerStats(getToken()),
    {
      refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
    }
  );

  const stats = dashboardData?.data?.stats || [];

  const quickActions = [
    {
      title: 'Add New Product',
      description: 'Create a new product listing',
      href: '/products/new',
      color: 'primary',
      icon: Package
    },
    {
      title: 'View Orders',
      description: 'Manage customer orders',
      href: '/orders',
      color: 'info',
      icon: ShoppingCart
    },
    {
      title: 'Customer Support',
      description: 'Help customers with their queries',
      href: '/customers',
      color: 'success',
      icon: Users
    },
    {
      title: 'Analytics',
      description: 'View detailed analytics',
      href: '/analytics',
      color: 'warning',
      icon: TrendingUp
    },
  ];

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Dashboard Overview
          </h1>
          <p className="text-gray-600">
            Welcome back! Here's what's happening with your store today.
          </p>
        </motion.div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <StatsCard {...stat} />
          </motion.div>
        ))}
      </div>

      {/* Charts and Tables Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Sales Chart */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="lg:col-span-2"
        >
          <SalesChart />
        </motion.div>

        {/* Customer Stats */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <CustomerStats />
        </motion.div>
      </div>

      {/* Recent Activity Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Recent Orders */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <RecentOrders />
        </motion.div>

        {/* Top Products */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <TopProducts />
        </motion.div>
      </div>

      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
        className="mb-8"
      >
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => (
              <motion.a
                key={action.title}
                href={action.href}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="group p-4 rounded-lg border border-gray-200 hover:border-primary-300 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center mb-3">
                  <div className={`p-2 rounded-lg bg-${action.color}-100 mr-3`}>
                    <action.icon className={`h-5 w-5 text-${action.color}-600`} />
                  </div>
                  <ArrowUpRight className="h-4 w-4 text-gray-400 group-hover:text-primary-600 transition-colors duration-200" />
                </div>
                <h4 className="font-medium text-gray-900 mb-1">{action.title}</h4>
                <p className="text-sm text-gray-600">{action.description}</p>
              </motion.a>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Performance Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.9 }}
        className="mb-8"
      >
        <div className="card p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Performance Insights</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-success-100 rounded-full mb-4">
                <TrendingUp className="h-6 w-6 text-success-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Revenue Growth</h4>
              <p className="text-sm text-gray-600 mb-2">
                Your revenue has increased by 12.5% compared to last month
              </p>
              <span className="inline-flex items-center text-sm font-medium text-success-600">
                <ArrowUpRight className="h-4 w-4 mr-1" />
                +$15,430
              </span>
            </div>

            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-info-100 rounded-full mb-4">
                <Eye className="h-6 w-6 text-info-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Page Views</h4>
              <p className="text-sm text-gray-600 mb-2">
                Your store received 45,230 page views this month
              </p>
              <span className="inline-flex items-center text-sm font-medium text-info-600">
                <ArrowUpRight className="h-4 w-4 mr-1" />
                +8.2%
              </span>
            </div>

            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-warning-100 rounded-full mb-4">
                <Users className="h-6 w-6 text-warning-600" />
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">New Customers</h4>
              <p className="text-sm text-gray-600 mb-2">
                234 new customers joined your store this month
              </p>
              <span className="inline-flex items-center text-sm font-medium text-warning-600">
                <ArrowUpRight className="h-4 w-4 mr-1" />
                +15.3%
              </span>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default DashboardPage;
