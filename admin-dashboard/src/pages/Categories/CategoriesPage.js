import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Tag,
  Package,
  Eye,
  MoreVertical
} from 'lucide-react';

import { categoriesAPI } from '../../services/api';
import { useAuthStore } from '../../store/authStore';
import LoadingSpinner from '../../components/UI/LoadingSpinner';

const CategoriesPage = () => {
  const { getToken } = useAuthStore();
  const [showCreateModal, setShowCreateModal] = useState(false);

  const { data: categoriesResponse, isLoading, refetch } = useQuery(
    'categories',
    () => categoriesAPI.getCategories(getToken())
  );

  const categories = categoriesResponse?.data?.categories || [];

  const getStatusColor = (isActive) => {
    return isActive 
      ? 'bg-success-100 text-success-800' 
      : 'bg-error-100 text-error-800';
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Categories</h1>
            <p className="text-gray-600">Organize your products with categories</p>
          </div>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setShowCreateModal(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="h-5 w-5" />
            <span>Add Category</span>
          </motion.button>
        </div>
      </div>

      {/* Categories Grid */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      ) : categories.length === 0 ? (
        <div className="text-center py-12">
          <div className="card p-12">
            <Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No categories yet</h3>
            <p className="text-gray-600 mb-6">
              Create categories to organize your products and make them easier to find.
            </p>
            <button 
              onClick={() => setShowCreateModal(true)}
              className="btn-primary"
            >
              Create Your First Category
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="card-hover p-6"
            >
              {/* Category Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-primary-100 rounded-lg">
                    <Tag className="h-6 w-6 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {category.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {category.slug}
                    </p>
                  </div>
                </div>
                
                <div className="relative">
                  <button className="p-1 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                    <MoreVertical className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Category Description */}
              {category.description && (
                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                  {category.description}
                </p>
              )}

              {/* Category Stats */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-1">
                    <Package className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      {category.productCount || 0} products
                    </span>
                  </div>
                </div>
                
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(category.isActive)}`}>
                  {category.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>

              {/* Category Actions */}
              <div className="flex items-center space-x-2 pt-4 border-t border-gray-200">
                <button className="flex-1 btn-outline text-sm py-2">
                  <Eye className="h-4 w-4 mr-2" />
                  View Products
                </button>
                <button className="p-2 text-gray-400 hover:text-warning-600 rounded-lg hover:bg-warning-50 transition-colors duration-200">
                  <Edit className="h-4 w-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-error-600 rounded-lg hover:bg-error-50 transition-colors duration-200">
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>

              {/* Featured Badge */}
              {category.isFeatured && (
                <div className="absolute top-4 right-4">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Featured
                  </span>
                </div>
              )}
            </motion.div>
          ))}
        </div>
      )}

      {/* Quick Stats */}
      {categories.length > 0 && (
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card p-6 text-center">
            <div className="text-2xl font-bold text-gray-900 mb-1">
              {categories.length}
            </div>
            <div className="text-sm text-gray-600">Total Categories</div>
          </div>
          
          <div className="card p-6 text-center">
            <div className="text-2xl font-bold text-gray-900 mb-1">
              {categories.filter(cat => cat.isActive).length}
            </div>
            <div className="text-sm text-gray-600">Active Categories</div>
          </div>
          
          <div className="card p-6 text-center">
            <div className="text-2xl font-bold text-gray-900 mb-1">
              {categories.reduce((sum, cat) => sum + (cat.productCount || 0), 0)}
            </div>
            <div className="text-sm text-gray-600">Total Products</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CategoriesPage;
