import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import toast from 'react-hot-toast';
import { 
  Eye, 
  EyeOff, 
  Lock, 
  Mail, 
  User, 
  Phone, 
  Store, 
  Globe,
  FileText,
  ArrowLeft,
  ShoppingBag
} from 'lucide-react';

import { authAPI } from '../../services/api';
import LoadingSpinner from '../../components/UI/LoadingSpinner';

// Validation schema
const schema = yup.object({
  // Personal Information
  name: yup
    .string()
    .required('Full name is required')
    .min(2, 'Name must be at least 2 characters'),
  email: yup
    .string()
    .email('Please enter a valid email')
    .required('Email is required'),
  password: yup
    .string()
    .min(8, 'Password must be at least 8 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase and number')
    .required('Password is required'),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref('password'), null], 'Passwords must match')
    .required('Please confirm your password'),
  phone: yup
    .string()
    .required('Phone number is required')
    .matches(/^\+966[0-9]{9}$/, 'Phone number must start with +966 and be followed by 9 digits (e.g., +966501234567)'),
  
  // Store Information
  storeName: yup
    .string()
    .required('Store name is required')
    .min(2, 'Store name must be at least 2 characters')
    .max(50, 'Store name must be less than 50 characters'),
  storeDomain: yup
    .string()
    .required('Store domain is required')
    .matches(/^[a-z0-9-]+$/, 'Domain can only contain lowercase letters, numbers, and hyphens')
    .min(3, 'Domain must be at least 3 characters')
    .max(30, 'Domain must be less than 30 characters'),
  storeDescription: yup
    .string()
    .max(500, 'Description must be less than 500 characters'),
});

const RegisterPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    trigger
  } = useForm({
    resolver: yupResolver(schema),
    mode: 'onChange'
  });

  const watchedStoreName = watch('storeName');
  const watchedStoreDomain = watch('storeDomain');

  // Auto-generate domain from store name
  React.useEffect(() => {
    if (watchedStoreName && !watchedStoreDomain) {
      const domain = watchedStoreName
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '-')
        .substring(0, 30);
      
      // Set the domain value
      document.querySelector('input[name="storeDomain"]').value = domain;
    }
  }, [watchedStoreName, watchedStoreDomain]);

  const onSubmit = async (data) => {
    try {
      setIsSubmitting(true);

      const registerData = {
        ...data,
        passwordConfirm: data.confirmPassword, // API expects passwordConfirm
        role: 'admin'
      };

      const response = await authAPI.register(registerData);
      
      if (response.success) {
        toast.success('Account created successfully! Redirecting to login...');
        // Redirect to login page after a short delay
        setTimeout(() => {
          window.location.href = '/login';
        }, 2000);
      }
    } catch (error) {
      console.error('Registration error:', error);

      let errorMessage = 'Registration failed';

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.error?.message) {
        errorMessage = error.response.data.error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Handle specific error cases
      if (errorMessage.includes('already exists')) {
        if (errorMessage.includes('email')) {
          errorMessage = 'An account with this email already exists. Please use a different email or try logging in.';
        } else if (errorMessage.includes('domain')) {
          errorMessage = 'This store domain is already taken. Please choose a different domain name.';
        }
      }

      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = async () => {
    const fieldsToValidate = currentStep === 1 
      ? ['name', 'email', 'password', 'confirmPassword', 'phone']
      : ['storeName', 'storeDomain', 'storeDescription'];
    
    const isValid = await trigger(fieldsToValidate);
    if (isValid) {
      setCurrentStep(2);
    }
  };

  const prevStep = () => {
    setCurrentStep(1);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex">
      {/* Left Side - Registration Form */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-md w-full space-y-8"
        >
          {/* Header */}
          <div className="text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto h-16 w-16 bg-gradient-primary rounded-2xl flex items-center justify-center mb-6"
            >
              <ShoppingBag className="h-8 w-8 text-white" />
            </motion.div>
            
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Create Your Store
            </h2>
            <p className="text-gray-600">
              Start your e-commerce journey today
            </p>
            
            {/* Progress Indicator */}
            <div className="flex items-center justify-center mt-6 space-x-2">
              <div className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                currentStep >= 1 ? 'bg-primary-600' : 'bg-gray-300'
              }`} />
              <div className={`w-8 h-1 rounded-full transition-colors duration-200 ${
                currentStep >= 2 ? 'bg-primary-600' : 'bg-gray-300'
              }`} />
              <div className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                currentStep >= 2 ? 'bg-primary-600' : 'bg-gray-300'
              }`} />
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-2 px-2">
              <span>Personal Info</span>
              <span>Store Details</span>
            </div>
          </div>

          {/* Registration Form */}
          <motion.form
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="mt-8 space-y-6"
            onSubmit={handleSubmit(onSubmit)}
          >
            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-4"
              >
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
                
                {/* Full Name */}
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <User className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      {...register('name')}
                      type="text"
                      className={`input pl-10 ${errors.name ? 'input-error' : ''}`}
                      placeholder="Enter your full name"
                    />
                  </div>
                  {errors.name && (
                    <p className="mt-1 text-sm text-error-600">{errors.name.message}</p>
                  )}
                </div>

                {/* Email */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      {...register('email')}
                      type="email"
                      className={`input pl-10 ${errors.email ? 'input-error' : ''}`}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  {errors.email && (
                    <p className="mt-1 text-sm text-error-600">{errors.email.message}</p>
                  )}
                </div>

                {/* Phone */}
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Phone className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      {...register('phone')}
                      type="tel"
                      className={`input pl-10 ${errors.phone ? 'input-error' : ''}`}
                      placeholder="+966501234567"
                      defaultValue="+966"
                    />
                  </div>
                  {errors.phone && (
                    <p className="mt-1 text-sm text-error-600">{errors.phone.message}</p>
                  )}
                </div>

                {/* Password */}
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                    Password *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      {...register('password')}
                      type={showPassword ? 'text' : 'password'}
                      className={`input pl-10 pr-10 ${errors.password ? 'input-error' : ''}`}
                      placeholder="Create a strong password"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                      )}
                    </button>
                  </div>
                  {errors.password && (
                    <p className="mt-1 text-sm text-error-600">{errors.password.message}</p>
                  )}
                </div>

                {/* Confirm Password */}
                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                    Confirm Password *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      {...register('confirmPassword')}
                      type={showConfirmPassword ? 'text' : 'password'}
                      className={`input pl-10 pr-10 ${errors.confirmPassword ? 'input-error' : ''}`}
                      placeholder="Confirm your password"
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                      ) : (
                        <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                      )}
                    </button>
                  </div>
                  {errors.confirmPassword && (
                    <p className="mt-1 text-sm text-error-600">{errors.confirmPassword.message}</p>
                  )}
                </div>

                {/* Next Button */}
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="button"
                  onClick={nextStep}
                  className="w-full btn-primary py-3 text-base font-medium"
                >
                  Continue to Store Setup →
                </motion.button>
              </motion.div>
            )}

            {/* Step 2: Store Information */}
            {currentStep === 2 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-4"
              >
                <div className="flex items-center space-x-2 mb-4">
                  <button
                    type="button"
                    onClick={prevStep}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  >
                    <ArrowLeft className="h-4 w-4" />
                  </button>
                  <h3 className="text-lg font-semibold text-gray-900">Store Information</h3>
                </div>

                {/* Store Name */}
                <div>
                  <label htmlFor="storeName" className="block text-sm font-medium text-gray-700 mb-2">
                    Store Name *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Store className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      {...register('storeName')}
                      type="text"
                      className={`input pl-10 ${errors.storeName ? 'input-error' : ''}`}
                      placeholder="My Awesome Store"
                    />
                  </div>
                  {errors.storeName && (
                    <p className="mt-1 text-sm text-error-600">{errors.storeName.message}</p>
                  )}
                </div>

                {/* Store Domain */}
                <div>
                  <label htmlFor="storeDomain" className="block text-sm font-medium text-gray-700 mb-2">
                    Store Domain *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Globe className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      {...register('storeDomain')}
                      type="text"
                      className={`input pl-10 ${errors.storeDomain ? 'input-error' : ''}`}
                      placeholder="my-awesome-store"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                      <span className="text-sm text-gray-500">.salla.com</span>
                    </div>
                  </div>
                  {errors.storeDomain && (
                    <p className="mt-1 text-sm text-error-600">{errors.storeDomain.message}</p>
                  )}
                  <p className="mt-1 text-sm text-gray-500">
                    This will be your store's web address
                  </p>
                </div>

                {/* Store Description */}
                <div>
                  <label htmlFor="storeDescription" className="block text-sm font-medium text-gray-700 mb-2">
                    Store Description
                  </label>
                  <div className="relative">
                    <div className="absolute top-3 left-3 pointer-events-none">
                      <FileText className="h-5 w-5 text-gray-400" />
                    </div>
                    <textarea
                      {...register('storeDescription')}
                      rows={3}
                      className={`input pl-10 ${errors.storeDescription ? 'input-error' : ''}`}
                      placeholder="Tell customers about your store..."
                    />
                  </div>
                  {errors.storeDescription && (
                    <p className="mt-1 text-sm text-error-600">{errors.storeDescription.message}</p>
                  )}
                </div>

                {/* Submit Button */}
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full btn-primary py-3 text-base font-medium relative overflow-hidden"
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center">
                      <LoadingSpinner size="sm" className="mr-2" />
                      Creating Your Store...
                    </div>
                  ) : (
                    'Create My Store'
                  )}
                </motion.button>
              </motion.div>
            )}

            {/* Login Link */}
            <div className="text-center">
              <p className="text-sm text-gray-600">
                Already have an account?{' '}
                <a href="/login" className="font-medium text-primary-600 hover:text-primary-500">
                  Sign in here
                </a>
              </p>
            </div>
          </motion.form>
        </motion.div>
      </div>

      {/* Right Side - Features Preview */}
      <div className="hidden lg:flex flex-1 bg-gradient-to-br from-primary-600 to-secondary-700 relative overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        
        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            <h1 className="text-4xl font-bold mb-6">
              Launch Your Online Store
            </h1>
            <p className="text-xl text-primary-100 mb-12 leading-relaxed">
              Join thousands of successful merchants who trust our platform to grow their business.
            </p>

            {/* Features List */}
            <div className="space-y-6">
              {[
                'Complete e-commerce solution',
                'Beautiful, responsive storefront',
                'Advanced analytics & reporting',
                'Secure payment processing',
                'Inventory management',
                '24/7 customer support'
              ].map((feature, index) => (
                <motion.div
                  key={feature}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                  className="flex items-center space-x-3"
                >
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                  <span className="text-primary-100">{feature}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-white bg-opacity-10 rounded-full -translate-y-32 translate-x-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-white bg-opacity-10 rounded-full translate-y-24 -translate-x-24"></div>
      </div>
    </div>
  );
};

export default RegisterPage;
