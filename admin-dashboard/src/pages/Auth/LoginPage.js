import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import toast from 'react-hot-toast';
import { Eye, EyeOff, Lock, Mail, ShoppingBag, TrendingUp, Users, Package } from 'lucide-react';

import { useAuthStore } from '../../store/authStore';
import LoadingSpinner from '../../components/UI/LoadingSpinner';

// Validation schema
const schema = yup.object({
  emailOrPhone: yup
    .string()
    .required('Email or phone number is required'),
  password: yup
    .string()
    .min(6, 'Password must be at least 6 characters')
    .required('Password is required'),
});

const LoginPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading } = useAuthStore();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const onSubmit = async (data) => {
    console.log('🔄 Login form submitted with data:', data);

    const result = await login(data.emailOrPhone, data.password);
    
    if (result.success) {
      toast.success('Welcome back, Admin! Redirecting...');
      console.log('✅ Login successful, redirect will happen automatically');
      // Redirect is now handled in authAPI.login()
    } else {
      console.error('Login error:', result.error);

      let errorMessage = result.error || 'Login failed';

      // Handle specific error cases
      if (errorMessage.includes('Invalid credentials')) {
        errorMessage = 'Invalid email or password. Please check your credentials and try again.';
      } else if (errorMessage.includes('not found')) {
        errorMessage = 'No account found with this email. Please check your email or register for a new account.';
      } else if (errorMessage.includes('Admin privileges')) {
        errorMessage = 'Access denied. This login is for store administrators only.';
      }

      toast.error(errorMessage);
    }
  };

  const stats = [
    { icon: ShoppingBag, label: 'Total Sales', value: '$125,430', color: 'text-green-600' },
    { icon: Package, label: 'Products', value: '1,234', color: 'text-blue-600' },
    { icon: Users, label: 'Customers', value: '5,678', color: 'text-purple-600' },
    { icon: TrendingUp, label: 'Growth', value: '+12.5%', color: 'text-pink-600' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex">
      {/* Left Side - Login Form */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-md w-full space-y-8"
        >
          {/* Header */}
          <div className="text-center">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="mx-auto h-16 w-16 bg-gradient-primary rounded-2xl flex items-center justify-center mb-6"
            >
              <ShoppingBag className="h-8 w-8 text-white" />
            </motion.div>
            
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Welcome Back, Admin
            </h2>
            <p className="text-gray-600">
              Sign in to your admin dashboard
            </p>
          </div>

          {/* Login Form */}
          <motion.form
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="mt-8 space-y-6"
            onSubmit={handleSubmit(onSubmit)}
          >
            <div className="space-y-4">
              {/* Email or Phone Field */}
              <div>
                <label htmlFor="emailOrPhone" className="block text-sm font-medium text-gray-700 mb-2">
                  Email or Phone Number
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    {...register('emailOrPhone')}
                    type="text"
                    autoComplete="username"
                    className={`input pl-10 ${errors.emailOrPhone ? 'input-error' : ''}`}
                    placeholder="<EMAIL> or +966501234567"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleSubmit(onSubmit)();
                      }
                    }}
                  />
                </div>
                {errors.emailOrPhone && (
                  <p className="mt-1 text-sm text-error-600">{errors.emailOrPhone.message}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  You can login with your email address or Saudi phone number
                </p>
              </div>

              {/* Password Field */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    {...register('password')}
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    className={`input pl-10 pr-10 ${errors.password ? 'input-error' : ''}`}
                    placeholder="Enter your password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400 hover:text-gray-600" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-error-600">{errors.password.message}</p>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="submit"
              disabled={isLoading}
              className="w-full btn-primary py-3 text-base font-medium relative overflow-hidden"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <LoadingSpinner size="sm" className="mr-2" />
                  Signing in...
                </div>
              ) : (
                'Sign In'
              )}
            </motion.button>

            {/* Register Link */}
            <div className="text-center mt-6">
              <p className="text-sm text-gray-600">
                Don't have a store yet?{' '}
                <a href="/register" className="font-medium text-primary-600 hover:text-primary-500">
                  Create your store
                </a>
              </p>
            </div>

            {/* Demo Credentials */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Demo Credentials:</h4>
              <div className="text-sm text-gray-600 space-y-1">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> AdminPass123</p>
              </div>
            </div>
          </motion.form>
        </motion.div>
      </div>

      {/* Right Side - Dashboard Preview */}
      <div className="hidden lg:flex flex-1 bg-gradient-to-br from-primary-600 to-secondary-700 relative overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        
        <div className="relative z-10 flex flex-col justify-center px-12 text-white">
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          >
            <h1 className="text-4xl font-bold mb-6">
              Powerful Admin Dashboard
            </h1>
            <p className="text-xl text-primary-100 mb-12 leading-relaxed">
              Manage your e-commerce store with our comprehensive admin panel. 
              Track sales, manage products, and grow your business.
            </p>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-6">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                  className="glass rounded-xl p-6"
                >
                  <div className="flex items-center mb-3">
                    <div className="p-2 bg-white bg-opacity-20 rounded-lg mr-3">
                      <stat.icon className="h-6 w-6 text-white" />
                    </div>
                    <span className="text-primary-100 text-sm font-medium">
                      {stat.label}
                    </span>
                  </div>
                  <div className="text-2xl font-bold text-white">
                    {stat.value}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-white bg-opacity-10 rounded-full -translate-y-32 translate-x-32"></div>
        <div className="absolute bottom-0 left-0 w-48 h-48 bg-white bg-opacity-10 rounded-full translate-y-24 -translate-x-24"></div>
      </div>
    </div>
  );
};

export default LoginPage;
