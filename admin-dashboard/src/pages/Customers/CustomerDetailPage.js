import React from 'react';
import { useParams } from 'react-router-dom';
import LoadingSpinner from '../../components/UI/LoadingSpinner';

const CustomerDetailPage = () => {
  const { id } = useParams();

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Customer Details</h1>
        <p className="text-gray-600">Customer ID: {id}</p>
      </div>
      
      <div className="card p-6">
        <p className="text-gray-600">Customer detail page content will be implemented here.</p>
      </div>
    </div>
  );
};

export default CustomerDetailPage;
