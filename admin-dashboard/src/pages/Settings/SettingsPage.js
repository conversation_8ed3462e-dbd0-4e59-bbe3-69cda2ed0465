import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Settings,
  Store,
  Palette,
  Globe,
  Moon,
  Sun,
  Save,
  Upload,
  Link,
  Phone,
  Mail,
  MapPin
} from 'lucide-react';
import toast from 'react-hot-toast';

// import { useTheme } from '../../contexts/ThemeContext';

const SettingsPage = () => {
  const { isDark, toggleTheme } = useTheme();
  const { language, changeLanguage, t, isRTL } = useLanguage();
  const { storeSettings, updateStoreSettings, getStoreUrl, isLoading } = useStore();

  const [activeTab, setActiveTab] = useState('general');
  const [formData, setFormData] = useState(storeSettings);

  const tabs = [
    { id: 'general', label: t('generalSettings'), icon: Settings },
    { id: 'store', label: t('storeSettings'), icon: Store },
    { id: 'appearance', label: t('appearance'), icon: Palette },
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    const result = await updateStoreSettings(formData);
    if (result.success) {
      toast.success('Settings saved successfully!');
    } else {
      toast.error('Failed to save settings');
    }
  };

  const handleLogoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        handleInputChange('logo', e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            {t('settings')}
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Manage your store settings and preferences
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          {/* Tabs */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                        : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                    }`}
                  >
                    <Icon className="w-5 h-5 mr-2" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {/* Content will be added in next step */}
              <div className="text-center py-12">
                <Settings className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  {activeTab === 'general' && 'General Settings'}
                  {activeTab === 'store' && 'Store Settings'}
                  {activeTab === 'appearance' && 'Appearance Settings'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Configure your {activeTab} preferences here.
                </p>
              </div>
            </motion.div>

            {/* Save Button */}
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={handleSave}
                disabled={isLoading}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                <Save className="w-4 h-4 mr-2" />
                {isLoading ? 'Saving...' : t('save')}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
