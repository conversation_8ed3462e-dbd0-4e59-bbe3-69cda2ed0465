{"name": "salla-admin-dashboard", "version": "1.0.0", "description": "Modern Admin Dashboard for Salla E-commerce Platform", "private": true, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "axios": "^1.5.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.4", "lucide-react": "^0.279.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-datepicker": "^4.18.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "react-select": "^5.7.7", "react-table": "^7.8.0", "recharts": "^2.8.0", "tailwindcss": "^3.3.3", "web-vitals": "^2.1.4", "yup": "^1.3.3", "zustand": "^4.4.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.15", "postcss": "^8.4.29"}}