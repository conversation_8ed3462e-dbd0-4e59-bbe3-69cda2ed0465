# 🛍️ Salla E-commerce Platform

A complete, modern e-commerce platform with **Backend API** and **Admin Dashboard** built with cutting-edge technologies.

## 🚀 Features

### 🔧 Backend API (Node.js + Express + MySQL)
- **Authentication & Authorization** - JWT-based auth with role management
- **Product Management** - Full CRUD with categories, variants, and inventory
- **Order Management** - Complete order lifecycle with status tracking
- **Customer Management** - User profiles, addresses, and order history
- **Shopping Cart** - Persistent cart with guest and user support
- **Coupons & Discounts** - Flexible coupon system with various types
- **Analytics & Reports** - Real-time dashboard statistics
- **Payment Integration** - Multiple payment method support
- **Shipping Management** - Shipping methods and cost calculation
- **Store Settings** - Configurable store information and settings

### 🎨 Admin Dashboard (React + Tailwind CSS)
- **Modern UI/UX** - Beautiful, responsive design with animations
- **Real-time Analytics** - Live dashboard with charts and statistics
- **Product Management** - Easy product creation and management
- **Order Processing** - Order management and status updates
- **Customer Insights** - Customer analytics and management
- **Coupon Management** - Create and manage discount coupons
- **Store Configuration** - Settings and store customization

## 🛠️ Tech Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MySQL** - Database with Sequelize ORM
- **JWT** - Authentication
- **Swagger** - API documentation
- **Winston** - Logging

### Admin Dashboard
- **React 18** - Frontend framework
- **Tailwind CSS** - Styling
- **Framer Motion** - Animations
- **React Query** - Data fetching
- **Zustand** - State management
- **Recharts** - Data visualization

## 📦 Quick Start

### 1. Backend Setup
```bash
# Install dependencies
npm install

# Create environment file
cp .env.example .env

# Configure database in .env file
# Start the server
npm run dev
```

### 2. Create Admin User
```bash
node create-admin.js
```

### 3. Add Sample Data
```bash
node create-test-data.js
node create-coupons.js
```

### 4. Admin Dashboard
```bash
cd admin-dashboard
npm install
npm start
```

## 🔗 Access Points

- **API Server**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api-docs
- **Admin Dashboard**: http://localhost:3001
- **Health Check**: http://localhost:3000/health

### Default Admin Credentials
- **Email**: <EMAIL>
- **Password**: AdminPass123

## 📚 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user profile

### Products
- `GET /api/products` - Get all products (with filtering & pagination)
- `GET /api/products/:id` - Get product by ID
- `POST /api/products` - Create new product (Admin/Merchant)

### Orders
- `GET /api/orders` - Get orders (filtered by user role)
- `POST /api/orders` - Create new order
- `PATCH /api/orders/:id/status` - Update order status

### Analytics (Admin Only)
- `GET /api/analytics/dashboard` - Dashboard statistics
- `GET /api/analytics/sales` - Sales data for charts
- `GET /api/analytics/top-products` - Top selling products
- `GET /api/analytics/recent-orders` - Recent orders

### Shopping Cart
- `GET /api/cart` - Get current cart
- `POST /api/cart/items` - Add item to cart
- `PATCH /api/cart/items/:itemId` - Update cart item

### Categories
- `GET /api/categories` - Get all categories
- `POST /api/categories` - Create category (Admin)

### Customers
- `GET /api/customers` - Get customers (Admin)
- `GET /api/customers/:id` - Get customer details

### Coupons
- `GET /api/coupons` - Get coupons (Admin)
- `POST /api/coupons` - Create coupon (Admin)
- `POST /api/coupons/validate` - Validate coupon

### Store
- `GET /api/store/info` - Get store information
- `GET /api/store/settings` - Get store settings

## 🧪 Testing

```bash
# Test basic API functionality
node test-api.js

# Test admin functionality
node test-admin.js

# Test analytics API
node test-analytics.js
```

## 🎯 Key Features

### 🔐 Security
- JWT authentication with role-based access
- Password hashing and validation
- Input sanitization and validation
- CORS and security headers

### 📊 Analytics
- Real-time dashboard statistics
- Sales tracking and revenue metrics
- Customer growth analytics
- Product performance insights

### 🛒 E-commerce
- Complete product catalog
- Shopping cart with persistence
- Order management system
- Coupon and discount system
- Inventory tracking

### 🎨 Modern UI
- Responsive admin dashboard
- Interactive charts and graphs
- Smooth animations
- Intuitive user interface

## 🔧 Environment Variables

```env
NODE_ENV=development
PORT=3000
DB_HOST=localhost
DB_NAME=salla_ecommerce
DB_USER=your_username
DB_PASSWORD=your_password
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d
```

## 🚀 Production Deployment

1. Set `NODE_ENV=production`
2. Configure production database
3. Set secure JWT secret
4. Enable HTTPS
5. Configure reverse proxy (nginx)
6. Set up monitoring and logging

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Commit changes
4. Push to branch
5. Create Pull Request

## 📄 License

MIT License - see LICENSE file for details.

---

**Built with ❤️ for modern e-commerce**
