const express = require('express');
const { body, validationResult } = require('express-validator');
const { AppError } = require('../middleware/errorHandler');
const { protect, restrictTo } = require('../middleware/auth');
const { Coupon } = require('../models');
const logger = require('../utils/logger');

const router = express.Router();


/**
 * @swagger
 * /api/coupons:
 *   get:
 *     summary: Get all coupons (Admin/Merchant only)
 *     tags: [Coupons]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: active
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [percentage, fixed, free_shipping]
 *         description: Filter by coupon type
 *     responses:
 *       200:
 *         description: Coupons retrieved successfully
 */
router.get('/', protect, restrictTo('admin', 'merchant'), async (req, res, next) => {
  try {
    const { Op } = require('sequelize');
    let where = {};

    // Apply filters
    if (req.query.active !== undefined) {
      where.active = req.query.active === 'true';
    }

    if (req.query.type) {
      where.type = req.query.type;
    }

    const coupons = await Coupon.findAll({
      where,
      order: [['createdAt', 'DESC']]
    });

    res.status(200).json({
      success: true,
      data: {
        coupons
      }
    });
  } catch (error) {
    logger.error('Get coupons error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/coupons/validate:
 *   post:
 *     summary: Validate a coupon code
 *     tags: [Coupons]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - orderTotal
 *             properties:
 *               code:
 *                 type: string
 *                 description: Coupon code
 *               orderTotal:
 *                 type: number
 *                 description: Order total amount
 *               items:
 *                 type: array
 *                 description: Order items for product-specific coupons
 *               customerId:
 *                 type: string
 *                 description: Customer ID for usage tracking
 *     responses:
 *       200:
 *         description: Coupon is valid
 *       400:
 *         description: Coupon is invalid or expired
 *       404:
 *         description: Coupon not found
 */
router.post('/validate', protect, [
  body('code')
    .trim()
    .notEmpty()
    .withMessage('Coupon code is required'),
  body('orderTotal')
    .isFloat({ min: 0 })
    .withMessage('Order total must be a positive number')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    const { code, orderTotal, items = [], customerId } = req.body;

    // Find coupon
    const coupon = coupons.find(c => c.code.toLowerCase() === code.toLowerCase());
    if (!coupon) {
      return next(new AppError('Coupon not found', 404));
    }

    // Check if coupon is active
    if (!coupon.active) {
      return next(new AppError('Coupon is not active', 400));
    }

    // Check date validity
    const now = new Date();
    const startDate = new Date(coupon.startDate);
    const endDate = new Date(coupon.endDate);

    if (now < startDate) {
      return next(new AppError('Coupon is not yet valid', 400));
    }

    if (now > endDate) {
      return next(new AppError('Coupon has expired', 400));
    }

    // Check minimum amount
    if (coupon.minimumAmount && orderTotal < coupon.minimumAmount) {
      return next(new AppError(`Minimum order amount of $${coupon.minimumAmount} required`, 400));
    }

    // Check usage limits
    if (coupon.usageLimit && coupon.usedCount >= coupon.usageLimit) {
      return next(new AppError('Coupon usage limit reached', 400));
    }

    // Check first-time customer restriction
    if (coupon.firstTimeCustomersOnly && customerId) {
      // TODO: Check if customer has previous orders
      // For simulation, assume customer is not first-time if they have customerId
      return next(new AppError('This coupon is only valid for first-time customers', 400));
    }

    // Calculate discount
    let discountAmount = 0;
    let freeShipping = false;

    switch (coupon.type) {
      case 'percentage':
        discountAmount = (orderTotal * coupon.value) / 100;
        if (coupon.maximumDiscount) {
          discountAmount = Math.min(discountAmount, coupon.maximumDiscount);
        }
        break;

      case 'fixed':
        discountAmount = Math.min(coupon.value, orderTotal);
        break;

      case 'free_shipping':
        freeShipping = true;
        discountAmount = 0; // Shipping discount calculated separately
        break;

      default:
        return next(new AppError('Invalid coupon type', 400));
    }

    // Round discount to 2 decimal places
    discountAmount = Math.round(discountAmount * 100) / 100;

    res.status(200).json({
      success: true,
      message: 'Coupon is valid',
      data: {
        coupon: {
          code: coupon.code,
          type: coupon.type,
          value: coupon.value,
          description: coupon.description
        },
        discount: {
          amount: discountAmount,
          freeShipping,
          finalTotal: Math.max(0, orderTotal - discountAmount)
        }
      }
    });
  } catch (error) {
    logger.error('Validate coupon error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/coupons:
 *   post:
 *     summary: Create a new coupon
 *     tags: [Coupons]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - code
 *               - type
 *               - value
 *               - description
 *             properties:
 *               code:
 *                 type: string
 *                 description: Unique coupon code
 *               type:
 *                 type: string
 *                 enum: [percentage, fixed, free_shipping]
 *               value:
 *                 type: number
 *                 description: Discount value
 *               description:
 *                 type: string
 *               minimumAmount:
 *                 type: number
 *               maximumDiscount:
 *                 type: number
 *               usageLimit:
 *                 type: number
 *               userLimit:
 *                 type: number
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *               firstTimeCustomersOnly:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Coupon created successfully
 *       400:
 *         description: Validation error or coupon code already exists
 */
router.post('/', protect, restrictTo('admin', 'merchant'), [
  body('code')
    .trim()
    .isLength({ min: 3, max: 20 })
    .matches(/^[A-Z0-9]+$/)
    .withMessage('Coupon code must be 3-20 characters, uppercase letters and numbers only'),
  body('type')
    .isIn(['percentage', 'fixed', 'free_shipping'])
    .withMessage('Invalid coupon type'),
  body('value')
    .isFloat({ min: 0 })
    .withMessage('Value must be a positive number'),
  body('description')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Description must be 5-200 characters'),
  body('startDate')
    .isISO8601()
    .withMessage('Start date must be a valid date'),
  body('endDate')
    .isISO8601()
    .withMessage('End date must be a valid date')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    const { code, type, value, description, minimumAmount, maximumDiscount, usageLimit, userLimit, startDate, endDate, firstTimeCustomersOnly } = req.body;

    // Check if coupon code already exists
    const existingCoupon = coupons.find(c => c.code.toLowerCase() === code.toLowerCase());
    if (existingCoupon) {
      return next(new AppError('Coupon code already exists', 400));
    }

    // Validate percentage value
    if (type === 'percentage' && value > 100) {
      return next(new AppError('Percentage value cannot exceed 100', 400));
    }

    // Validate date range
    if (new Date(startDate) >= new Date(endDate)) {
      return next(new AppError('End date must be after start date', 400));
    }

    // Create new coupon
    const newCoupon = {
      id: code,
      code: code.toUpperCase(),
      type,
      value,
      description,
      minimumAmount: minimumAmount || 0,
      maximumDiscount,
      usageLimit,
      usedCount: 0,
      userLimit,
      startDate,
      endDate,
      active: true,
      applicableProducts: [],
      applicableCategories: [],
      excludedProducts: [],
      firstTimeCustomersOnly: firstTimeCustomersOnly || false
    };

    // Add to coupons array (in real app, save to database)
    coupons.push(newCoupon);

    res.status(201).json({
      success: true,
      message: 'Coupon created successfully',
      data: {
        coupon: newCoupon
      }
    });
  } catch (error) {
    logger.error('Create coupon error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/coupons/{code}:
 *   patch:
 *     summary: Update coupon
 *     tags: [Coupons]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Coupon updated successfully
 *       404:
 *         description: Coupon not found
 */
router.patch('/:code', protect, restrictTo('admin', 'merchant'), async (req, res, next) => {
  try {
    const couponIndex = coupons.findIndex(c => c.code.toLowerCase() === req.params.code.toLowerCase());
    if (couponIndex === -1) {
      return next(new AppError('Coupon not found', 404));
    }

    // Update coupon
    const updatedCoupon = { ...coupons[couponIndex], ...req.body };
    coupons[couponIndex] = updatedCoupon;

    res.status(200).json({
      success: true,
      message: 'Coupon updated successfully',
      data: {
        coupon: updatedCoupon
      }
    });
  } catch (error) {
    logger.error('Update coupon error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/coupons/{code}:
 *   delete:
 *     summary: Delete coupon
 *     tags: [Coupons]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Coupon deleted successfully
 *       404:
 *         description: Coupon not found
 */
router.delete('/:code', protect, restrictTo('admin'), async (req, res, next) => {
  try {
    const couponIndex = coupons.findIndex(c => c.code.toLowerCase() === req.params.code.toLowerCase());
    if (couponIndex === -1) {
      return next(new AppError('Coupon not found', 404));
    }

    // Remove coupon
    coupons.splice(couponIndex, 1);

    res.status(200).json({
      success: true,
      message: 'Coupon deleted successfully'
    });
  } catch (error) {
    logger.error('Delete coupon error:', error);
    next(error);
  }
});

module.exports = router;
