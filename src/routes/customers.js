const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { User, Order } = require('../models');
const { AppError } = require('../middleware/errorHandler');
const { protect, restrictTo } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @swagger
 * /api/customers:
 *   get:
 *     summary: Get all customers (Admin/Merchant only)
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by name or email
 *       - in: query
 *         name: active
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *     responses:
 *       200:
 *         description: Customers retrieved successfully
 *       403:
 *         description: Not authorized
 */
router.get('/', protect, restrictTo('admin', 'merchant'), async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Build query
    const { Op } = require('sequelize');
    let where = { role: 'customer' };

    if (req.query.search) {
      where[Op.or] = [
        { name: { [Op.like]: `%${req.query.search}%` } },
        { email: { [Op.like]: `%${req.query.search}%` } }
      ];
    }

    if (req.query.active !== undefined) {
      where.active = req.query.active === 'true';
    }

    const { count, rows: customers } = await User.findAndCountAll({
      where,
      attributes: { exclude: ['password', 'passwordResetToken', 'passwordResetExpires', 'emailVerificationToken'] },
      order: [['createdAt', 'DESC']],
      offset: skip,
      limit
    });

    const total = count;
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      data: {
        customers,
        pagination: {
          currentPage: page,
          totalPages,
          totalCustomers: total,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit
        }
      }
    });
  } catch (error) {
    logger.error('Get customers error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/customers/{id}:
 *   get:
 *     summary: Get customer by ID
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Customer retrieved successfully
 *       404:
 *         description: Customer not found
 *       403:
 *         description: Not authorized
 */
router.get('/:id', protect, restrictTo('admin', 'merchant'), async (req, res, next) => {
  try {
    const customer = await User.findOne({ 
      _id: req.params.id, 
      role: 'customer' 
    }).select('-password -passwordResetToken -passwordResetExpires -emailVerificationToken');

    if (!customer) {
      return next(new AppError('Customer not found', 404));
    }

    // Get customer statistics
    const orderStats = await Order.aggregate([
      { $match: { customer: customer._id } },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalSpent: { $sum: '$totalAmount' },
          averageOrderValue: { $avg: '$totalAmount' }
        }
      }
    ]);

    const stats = orderStats[0] || {
      totalOrders: 0,
      totalSpent: 0,
      averageOrderValue: 0
    };

    res.status(200).json({
      success: true,
      data: {
        customer: {
          ...customer.toObject(),
          stats
        }
      }
    });
  } catch (error) {
    logger.error('Get customer error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/customers/{id}/orders:
 *   get:
 *     summary: Get customer's orders
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Customer orders retrieved successfully
 *       404:
 *         description: Customer not found
 */
router.get('/:id/orders', protect, restrictTo('admin', 'merchant'), async (req, res, next) => {
  try {
    const customer = await User.findOne({ 
      _id: req.params.id, 
      role: 'customer' 
    });

    if (!customer) {
      return next(new AppError('Customer not found', 404));
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    let query = { customer: customer._id };
    
    if (req.query.status) {
      query.status = req.query.status;
    }

    const orders = await Order.find(query)
      .populate('items.product', 'name images')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Order.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: page,
          totalPages,
          totalOrders: total,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit
        }
      }
    });
  } catch (error) {
    logger.error('Get customer orders error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/customers/{id}/addresses:
 *   post:
 *     summary: Add address to customer
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - lastName
 *               - address1
 *               - city
 *               - state
 *               - postalCode
 *               - country
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [home, work, other]
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               company:
 *                 type: string
 *               address1:
 *                 type: string
 *               address2:
 *                 type: string
 *               city:
 *                 type: string
 *               state:
 *                 type: string
 *               postalCode:
 *                 type: string
 *               country:
 *                 type: string
 *               phone:
 *                 type: string
 *               isDefault:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Address added successfully
 *       404:
 *         description: Customer not found
 */
router.post('/:id/addresses', protect, [
  body('firstName').trim().notEmpty().withMessage('First name is required'),
  body('lastName').trim().notEmpty().withMessage('Last name is required'),
  body('address1').trim().notEmpty().withMessage('Address is required'),
  body('city').trim().notEmpty().withMessage('City is required'),
  body('state').trim().notEmpty().withMessage('State is required'),
  body('postalCode').trim().notEmpty().withMessage('Postal code is required'),
  body('country').trim().notEmpty().withMessage('Country is required'),
  body('type').optional().isIn(['home', 'work', 'other']).withMessage('Invalid address type')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    // Check if user can modify this customer's data
    if (req.user.role === 'customer' && req.user.id !== req.params.id) {
      return next(new AppError('You can only modify your own addresses', 403));
    }

    const customer = await User.findOne({ 
      _id: req.params.id, 
      role: 'customer' 
    });

    if (!customer) {
      return next(new AppError('Customer not found', 404));
    }

    const addressData = req.body;

    // If this is set as default, unset other default addresses
    if (addressData.isDefault) {
      customer.addresses.forEach(addr => {
        addr.isDefault = false;
      });
    }

    customer.addresses.push(addressData);
    await customer.save();

    res.status(200).json({
      success: true,
      message: 'Address added successfully',
      data: {
        customer
      }
    });
  } catch (error) {
    logger.error('Add customer address error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/customers/{id}/addresses/{addressId}:
 *   patch:
 *     summary: Update customer address
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: addressId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Address updated successfully
 *       404:
 *         description: Customer or address not found
 */
router.patch('/:id/addresses/:addressId', protect, async (req, res, next) => {
  try {
    // Check if user can modify this customer's data
    if (req.user.role === 'customer' && req.user.id !== req.params.id) {
      return next(new AppError('You can only modify your own addresses', 403));
    }

    const customer = await User.findOne({ 
      _id: req.params.id, 
      role: 'customer' 
    });

    if (!customer) {
      return next(new AppError('Customer not found', 404));
    }

    const address = customer.addresses.id(req.params.addressId);
    if (!address) {
      return next(new AppError('Address not found', 404));
    }

    // Update address fields
    Object.keys(req.body).forEach(key => {
      if (req.body[key] !== undefined) {
        address[key] = req.body[key];
      }
    });

    // If this is set as default, unset other default addresses
    if (req.body.isDefault) {
      customer.addresses.forEach(addr => {
        if (addr._id.toString() !== req.params.addressId) {
          addr.isDefault = false;
        }
      });
    }

    await customer.save();

    res.status(200).json({
      success: true,
      message: 'Address updated successfully',
      data: {
        customer
      }
    });
  } catch (error) {
    logger.error('Update customer address error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/customers/{id}/addresses/{addressId}:
 *   delete:
 *     summary: Delete customer address
 *     tags: [Customers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: addressId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Address deleted successfully
 *       404:
 *         description: Customer or address not found
 */
router.delete('/:id/addresses/:addressId', protect, async (req, res, next) => {
  try {
    // Check if user can modify this customer's data
    if (req.user.role === 'customer' && req.user.id !== req.params.id) {
      return next(new AppError('You can only modify your own addresses', 403));
    }

    const customer = await User.findOne({ 
      _id: req.params.id, 
      role: 'customer' 
    });

    if (!customer) {
      return next(new AppError('Customer not found', 404));
    }

    const address = customer.addresses.id(req.params.addressId);
    if (!address) {
      return next(new AppError('Address not found', 404));
    }

    customer.addresses.pull(req.params.addressId);
    await customer.save();

    res.status(200).json({
      success: true,
      message: 'Address deleted successfully',
      data: {
        customer
      }
    });
  } catch (error) {
    logger.error('Delete customer address error:', error);
    next(error);
  }
});

module.exports = router;
