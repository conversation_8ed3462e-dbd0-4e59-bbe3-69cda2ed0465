const express = require('express');
const { Op, fn, col, literal } = require('sequelize');
const { protect, restrictTo } = require('../middleware/auth');
const { User, Product, Order, Category, Store, sequelize } = require('../models');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

const router = express.Router();

// Protect all routes - admin only
router.use(protect);
router.use(restrictTo('admin'));

// Helper function to get user's store
async function getUserStore(userId) {
  try {
    const store = await Store.findOne({ where: { ownerId: userId } });
    return store ? store.id : 1; // Default to store ID 1 if no store found
  } catch (error) {
    console.log('Error getting user store:', error.message);
    return 1; // Default to store ID 1
  }
}

/**
 * @swagger
 * /api/analytics/dashboard:
 *   get:
 *     summary: Get dashboard statistics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 */
router.get('/dashboard', async (req, res, next) => {
  try {
    const storeId = await getUserStore(req.user.id);
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    const lastYear = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());

    // Get total revenue (handle case where no orders exist)
    let totalRevenue = 0;
    try {
      totalRevenue = await Order.sum('totalAmount', {
        where: {
          storeId: storeId,
          paymentStatus: 'paid',
          createdAt: { [Op.gte]: lastYear }
        }
      }) || 0;
    } catch (error) {
      console.log('Revenue calculation error:', error.message);
      totalRevenue = 0;
    }

    // Get last month revenue for comparison
    let lastMonthRevenue = 0;
    try {
      lastMonthRevenue = await Order.sum('totalAmount', {
        where: {
          storeId: storeId,
          paymentStatus: 'paid',
          createdAt: {
            [Op.gte]: lastMonth,
            [Op.lt]: now
          }
        }
      }) || 0;
    } catch (error) {
      console.log('Last month revenue calculation error:', error.message);
      lastMonthRevenue = 0;
    }

    // Simplified stats for now
    const totalOrders = 0;
    const totalCustomers = 0;
    const totalProducts = 0;

    // Simplified calculations
    const revenueChange = 0;
    const ordersChange = 0;

    // Simplified stats
    const customersChange = 0;
    const productsChange = 0;

    res.status(200).json({
      success: true,
      data: {
        stats: [
          {
            title: 'Total Revenue',
            value: `$${lastMonthRevenue.toLocaleString()}`,
            change: `${revenueChange >= 0 ? '+' : ''}${revenueChange}%`,
            trend: revenueChange >= 0 ? 'up' : 'down',
            icon: 'DollarSign',
            color: 'success',
            description: 'vs last month'
          },
          {
            title: 'Total Orders',
            value: totalOrders.toLocaleString(),
            change: `${ordersChange >= 0 ? '+' : ''}${ordersChange}%`,
            trend: ordersChange >= 0 ? 'up' : 'down',
            icon: 'ShoppingCart',
            color: 'info',
            description: 'vs last month'
          },
          {
            title: 'Total Customers',
            value: totalCustomers.toLocaleString(),
            change: `${customersChange >= 0 ? '+' : ''}${customersChange}%`,
            trend: customersChange >= 0 ? 'up' : 'down',
            icon: 'Users',
            color: 'primary',
            description: 'vs last month'
          },
          {
            title: 'Total Products',
            value: totalProducts.toLocaleString(),
            change: `${productsChange >= 0 ? '+' : ''}${productsChange}%`,
            trend: productsChange >= 0 ? 'up' : 'down',
            icon: 'Package',
            color: 'warning',
            description: 'vs last month'
          }
        ]
      }
    });
  } catch (error) {
    logger.error('Get dashboard stats error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/analytics/sales:
 *   get:
 *     summary: Get sales data for charts
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d]
 *         description: Time period for sales data
 *     responses:
 *       200:
 *         description: Sales data retrieved successfully
 */
router.get('/sales', async (req, res, next) => {
  try {
    const { period = '7d' } = req.query;
    const now = new Date();
    let startDate, groupBy, dateFormat;

    switch (period) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        groupBy = 'DATE(created_at)';
        dateFormat = '%a'; // Day name (Mon, Tue, etc.)
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        groupBy = 'WEEK(created_at)';
        dateFormat = 'Week %u';
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        groupBy = 'MONTH(created_at)';
        dateFormat = '%M';
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        groupBy = 'DATE(created_at)';
        dateFormat = '%a';
    }

    const salesData = await Order.findAll({
      attributes: [
        [fn('DATE_FORMAT', col('created_at'), dateFormat), 'name'],
        [fn('SUM', col('total_amount')), 'sales'],
        [fn('COUNT', col('id')), 'orders']
      ],
      where: {
        createdAt: { [Op.gte]: startDate },
        paymentStatus: 'paid'
      },
      group: [fn('DATE_FORMAT', col('created_at'), dateFormat)],
      order: [[fn('DATE_FORMAT', col('created_at'), dateFormat), 'ASC']],
      raw: true
    });

    // Format the data
    const formattedData = salesData.map(item => ({
      name: item.name,
      sales: parseFloat(item.sales) || 0,
      orders: parseInt(item.orders) || 0
    }));

    res.status(200).json({
      success: true,
      data: {
        salesData: formattedData,
        period
      }
    });
  } catch (error) {
    logger.error('Get sales data error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/analytics/top-products:
 *   get:
 *     summary: Get top selling products
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 5
 *         description: Number of top products to return
 *     responses:
 *       200:
 *         description: Top products retrieved successfully
 */
router.get('/top-products', async (req, res, next) => {
  try {
    const { limit = 5 } = req.query;

    const topProducts = await Product.findAll({
      attributes: [
        'id',
        'name',
        'price',
        'images',
        'salesCount'
      ],
      where: {
        status: 'active'
      },
      order: [['salesCount', 'DESC']],
      limit: parseInt(limit)
    });

    res.status(200).json({
      success: true,
      data: {
        topProducts
      }
    });
  } catch (error) {
    logger.error('Get top products error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/analytics/recent-orders:
 *   get:
 *     summary: Get recent orders
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 5
 *         description: Number of recent orders to return
 *     responses:
 *       200:
 *         description: Recent orders retrieved successfully
 */
router.get('/recent-orders', async (req, res, next) => {
  try {
    const { limit = 5 } = req.query;

    const recentOrders = await Order.findAll({
      attributes: [
        'id',
        'orderNumber',
        'totalAmount',
        'status',
        'paymentStatus',
        'createdAt'
      ],
      include: [
        {
          model: User,
          as: 'customer',
          attributes: ['id', 'name', 'email']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit)
    });

    res.status(200).json({
      success: true,
      data: {
        recentOrders
      }
    });
  } catch (error) {
    logger.error('Get recent orders error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/analytics/customer-stats:
 *   get:
 *     summary: Get customer statistics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Customer statistics retrieved successfully
 */
router.get('/customer-stats', async (req, res, next) => {
  try {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());

    // New customers this month
    const newCustomers = await User.count({
      where: {
        role: 'customer',
        createdAt: { [Op.gte]: lastMonth }
      }
    });

    // Total customers
    const totalCustomers = await User.count({
      where: {
        role: 'customer'
      }
    });

    // Active customers (customers who made an order in last 30 days)
    const activeCustomers = await User.count({
      where: {
        role: 'customer'
      },
      include: [
        {
          model: Order,
          as: 'orders',
          where: {
            createdAt: { [Op.gte]: lastMonth }
          },
          required: true
        }
      ]
    });

    // Customer growth rate
    const previousMonth = new Date(now.getFullYear(), now.getMonth() - 2, now.getDate());
    const previousMonthCustomers = await User.count({
      where: {
        role: 'customer',
        createdAt: { 
          [Op.gte]: previousMonth,
          [Op.lt]: lastMonth
        }
      }
    });

    const growthRate = previousMonthCustomers > 0 
      ? ((newCustomers - previousMonthCustomers) / previousMonthCustomers * 100).toFixed(1)
      : 0;

    res.status(200).json({
      success: true,
      data: {
        customerStats: {
          newCustomers,
          totalCustomers,
          activeCustomers,
          growthRate: `${growthRate >= 0 ? '+' : ''}${growthRate}%`
        }
      }
    });
  } catch (error) {
    logger.error('Get customer stats error:', error);
    next(error);
  }
});

module.exports = router;
