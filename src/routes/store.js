const express = require('express');
const { body, validationResult } = require('express-validator');
const { AppError } = require('../middleware/errorHandler');
const { protect, restrictTo } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Simulate store settings (in real app, this would be in database)
let storeSettings = {
  general: {
    storeName: 'My E-commerce Store',
    storeDescription: 'Your one-stop shop for quality products',
    storeEmail: '<EMAIL>',
    storePhone: '******-0123',
    storeAddress: {
      street: '123 Commerce St',
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      country: 'US'
    },
    timezone: 'America/New_York',
    dateFormat: 'MM/DD/YYYY',
    timeFormat: '12h'
  },
  currency: {
    defaultCurrency: 'USD',
    supportedCurrencies: ['USD', 'EUR', 'GBP', 'CAD', 'SAR'],
    currencyPosition: 'before',
    decimalSeparator: '.',
    thousandSeparator: ','
  },
  tax: {
    enableTax: true,
    taxIncluded: false,
    defaultTaxRate: 8.5,
    taxRates: [
      { region: 'NY', rate: 8.5 },
      { region: 'CA', rate: 7.25 },
      { region: 'TX', rate: 6.25 }
    ]
  },
  shipping: {
    enableShipping: true,
    freeShippingThreshold: 100,
    defaultShippingCost: 9.99,
    shippingZones: [
      {
        name: 'Domestic',
        countries: ['US'],
        rates: { standard: 9.99, express: 19.99 }
      },
      {
        name: 'International',
        countries: ['*'],
        rates: { standard: 24.99, express: 49.99 }
      }
    ]
  },
  payment: {
    enabledMethods: ['credit_card', 'paypal', 'stripe', 'cash_on_delivery'],
    defaultMethod: 'credit_card',
    testMode: true,
    paymentGateways: {
      stripe: {
        enabled: true,
        publicKey: 'pk_test_...',
        secretKey: 'sk_test_...'
      },
      paypal: {
        enabled: true,
        clientId: 'paypal_client_id',
        clientSecret: 'paypal_client_secret',
        mode: 'sandbox'
      }
    }
  },
  inventory: {
    trackInventory: true,
    allowBackorders: false,
    lowStockThreshold: 5,
    outOfStockVisibility: 'hidden'
  },
  notifications: {
    orderNotifications: true,
    lowStockNotifications: true,
    customerNotifications: true,
    emailNotifications: {
      orderConfirmation: true,
      orderShipped: true,
      orderDelivered: true,
      passwordReset: true
    },
    smsNotifications: {
      orderConfirmation: false,
      orderShipped: true,
      orderDelivered: false
    }
  },
  seo: {
    metaTitle: 'My E-commerce Store - Quality Products Online',
    metaDescription: 'Shop quality products at competitive prices with fast shipping and excellent customer service.',
    metaKeywords: 'ecommerce, online shopping, quality products',
    enableSitemap: true,
    enableRobotsTxt: true
  },
  social: {
    facebook: 'https://facebook.com/mystore',
    twitter: 'https://twitter.com/mystore',
    instagram: 'https://instagram.com/mystore',
    linkedin: 'https://linkedin.com/company/mystore'
  },
  analytics: {
    googleAnalytics: 'GA_TRACKING_ID',
    facebookPixel: 'FB_PIXEL_ID',
    enableTracking: true
  }
};

/**
 * @swagger
 * /api/store/settings:
 *   get:
 *     summary: Get store settings
 *     tags: [Store]
 *     parameters:
 *       - in: query
 *         name: section
 *         schema:
 *           type: string
 *           enum: [general, currency, tax, shipping, payment, inventory, notifications, seo, social, analytics]
 *         description: Get specific section of settings
 *     responses:
 *       200:
 *         description: Store settings retrieved successfully
 */
router.get('/settings', async (req, res, next) => {
  try {
    let settings = storeSettings;

    // Return specific section if requested
    if (req.query.section && storeSettings[req.query.section]) {
      settings = { [req.query.section]: storeSettings[req.query.section] };
    }

    // Remove sensitive information for non-admin users
    if (!req.user || req.user.role !== 'admin') {
      const publicSettings = {
        general: {
          storeName: settings.general?.storeName,
          storeDescription: settings.general?.storeDescription,
          storeEmail: settings.general?.storeEmail,
          storePhone: settings.general?.storePhone,
          timezone: settings.general?.timezone
        },
        currency: settings.currency,
        tax: {
          enableTax: settings.tax?.enableTax,
          taxIncluded: settings.tax?.taxIncluded
        },
        shipping: {
          enableShipping: settings.shipping?.enableShipping,
          freeShippingThreshold: settings.shipping?.freeShippingThreshold
        },
        payment: {
          enabledMethods: settings.payment?.enabledMethods,
          defaultMethod: settings.payment?.defaultMethod
        }
      };
      settings = req.query.section ? { [req.query.section]: publicSettings[req.query.section] } : publicSettings;
    }

    res.status(200).json({
      success: true,
      data: {
        settings
      }
    });
  } catch (error) {
    logger.error('Get store settings error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/store/settings:
 *   patch:
 *     summary: Update store settings
 *     tags: [Store]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               general:
 *                 type: object
 *               currency:
 *                 type: object
 *               tax:
 *                 type: object
 *               shipping:
 *                 type: object
 *               payment:
 *                 type: object
 *               inventory:
 *                 type: object
 *               notifications:
 *                 type: object
 *               seo:
 *                 type: object
 *               social:
 *                 type: object
 *               analytics:
 *                 type: object
 *     responses:
 *       200:
 *         description: Store settings updated successfully
 *       403:
 *         description: Not authorized
 */
router.patch('/settings', protect, restrictTo('admin'), async (req, res, next) => {
  try {
    const updates = req.body;

    // Update each section
    Object.keys(updates).forEach(section => {
      if (storeSettings[section]) {
        storeSettings[section] = { ...storeSettings[section], ...updates[section] };
      }
    });

    res.status(200).json({
      success: true,
      message: 'Store settings updated successfully',
      data: {
        settings: storeSettings
      }
    });
  } catch (error) {
    logger.error('Update store settings error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/store/info:
 *   get:
 *     summary: Get public store information
 *     tags: [Store]
 *     responses:
 *       200:
 *         description: Store information retrieved successfully
 */
router.get('/info', async (req, res, next) => {
  try {
    const storeInfo = {
      name: storeSettings.general.storeName,
      description: storeSettings.general.storeDescription,
      email: storeSettings.general.storeEmail,
      phone: storeSettings.general.storePhone,
      address: storeSettings.general.storeAddress,
      currency: storeSettings.currency.defaultCurrency,
      supportedCurrencies: storeSettings.currency.supportedCurrencies,
      freeShippingThreshold: storeSettings.shipping.freeShippingThreshold,
      paymentMethods: storeSettings.payment.enabledMethods,
      social: storeSettings.social,
      policies: {
        termsOfService: '/terms',
        privacyPolicy: '/privacy',
        returnPolicy: '/returns',
        shippingPolicy: '/shipping'
      }
    };

    res.status(200).json({
      success: true,
      data: {
        store: storeInfo
      }
    });
  } catch (error) {
    logger.error('Get store info error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/store/stats:
 *   get:
 *     summary: Get store statistics
 *     tags: [Store]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [today, week, month, year]
 *           default: month
 *         description: Statistics period
 *     responses:
 *       200:
 *         description: Store statistics retrieved successfully
 *       403:
 *         description: Not authorized
 */
router.get('/stats', protect, restrictTo('admin', 'merchant'), async (req, res, next) => {
  try {
    const period = req.query.period || 'month';

    // Simulate statistics (in real app, calculate from database)
    const stats = {
      period,
      sales: {
        totalRevenue: 45678.90,
        totalOrders: 234,
        averageOrderValue: 195.25,
        conversionRate: 3.2
      },
      products: {
        totalProducts: 156,
        activeProducts: 142,
        outOfStock: 8,
        lowStock: 12
      },
      customers: {
        totalCustomers: 1245,
        newCustomers: 67,
        returningCustomers: 178,
        customerLifetimeValue: 456.78
      },
      traffic: {
        totalVisitors: 12456,
        uniqueVisitors: 8934,
        pageViews: 34567,
        bounceRate: 45.6
      },
      topProducts: [
        { id: '1', name: 'Product A', sales: 45, revenue: 2250 },
        { id: '2', name: 'Product B', sales: 38, revenue: 1900 },
        { id: '3', name: 'Product C', sales: 32, revenue: 1600 }
      ],
      topCategories: [
        { id: '1', name: 'Electronics', sales: 123, revenue: 12300 },
        { id: '2', name: 'Clothing', sales: 89, revenue: 8900 },
        { id: '3', name: 'Home & Garden', sales: 67, revenue: 6700 }
      ],
      recentOrders: [
        { id: 'ORD-001', customer: 'John Doe', total: 125.50, status: 'completed' },
        { id: 'ORD-002', customer: 'Jane Smith', total: 89.99, status: 'processing' },
        { id: 'ORD-003', customer: 'Bob Johnson', total: 234.75, status: 'shipped' }
      ]
    };

    res.status(200).json({
      success: true,
      data: {
        stats
      }
    });
  } catch (error) {
    logger.error('Get store stats error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/store/currencies:
 *   get:
 *     summary: Get supported currencies with exchange rates
 *     tags: [Store]
 *     responses:
 *       200:
 *         description: Currencies retrieved successfully
 */
router.get('/currencies', async (req, res, next) => {
  try {
    // Simulate exchange rates (in real app, fetch from currency API)
    const currencies = [
      { code: 'USD', name: 'US Dollar', symbol: '$', rate: 1.0 },
      { code: 'EUR', name: 'Euro', symbol: '€', rate: 0.85 },
      { code: 'GBP', name: 'British Pound', symbol: '£', rate: 0.73 },
      { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', rate: 1.25 },
      { code: 'SAR', name: 'Saudi Riyal', symbol: 'ر.س', rate: 3.75 }
    ];

    const supportedCurrencies = currencies.filter(currency => 
      storeSettings.currency.supportedCurrencies.includes(currency.code)
    );

    res.status(200).json({
      success: true,
      data: {
        defaultCurrency: storeSettings.currency.defaultCurrency,
        currencies: supportedCurrencies,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('Get currencies error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/store/policies:
 *   get:
 *     summary: Get store policies
 *     tags: [Store]
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [terms, privacy, returns, shipping]
 *         description: Policy type
 *     responses:
 *       200:
 *         description: Store policies retrieved successfully
 */
router.get('/policies', async (req, res, next) => {
  try {
    const policies = {
      terms: {
        title: 'Terms of Service',
        content: 'These terms and conditions outline the rules and regulations for the use of our website...',
        lastUpdated: '2024-01-01'
      },
      privacy: {
        title: 'Privacy Policy',
        content: 'This Privacy Policy describes how we collect, use, and protect your information...',
        lastUpdated: '2024-01-01'
      },
      returns: {
        title: 'Return Policy',
        content: 'We offer a 30-day return policy for most items. Items must be in original condition...',
        lastUpdated: '2024-01-01'
      },
      shipping: {
        title: 'Shipping Policy',
        content: 'We offer various shipping options to meet your needs. Standard shipping takes 5-7 business days...',
        lastUpdated: '2024-01-01'
      }
    };

    const result = req.query.type && policies[req.query.type] 
      ? { [req.query.type]: policies[req.query.type] }
      : policies;

    res.status(200).json({
      success: true,
      data: {
        policies: result
      }
    });
  } catch (error) {
    logger.error('Get store policies error:', error);
    next(error);
  }
});

module.exports = router;
