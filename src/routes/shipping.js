const express = require('express');
const { body, validationResult } = require('express-validator');
const { AppError } = require('../middleware/errorHandler');
const { protect, restrictTo } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @swagger
 * /api/shipping/methods:
 *   get:
 *     summary: Get available shipping methods
 *     tags: [Shipping]
 *     parameters:
 *       - in: query
 *         name: country
 *         schema:
 *           type: string
 *         description: Destination country code
 *       - in: query
 *         name: weight
 *         schema:
 *           type: number
 *         description: Package weight in kg
 *       - in: query
 *         name: value
 *         schema:
 *           type: number
 *         description: Package value for insurance
 *     responses:
 *       200:
 *         description: Shipping methods retrieved successfully
 */
router.get('/methods', async (req, res, next) => {
  try {
    const { country = 'US', weight = 1, value = 100 } = req.query;

    // Simulate shipping methods based on destination
    const shippingMethods = [
      {
        id: 'standard',
        name: 'Standard Shipping',
        description: '5-7 business days',
        price: 9.99,
        estimatedDays: '5-7',
        carrier: 'Local Post',
        trackingAvailable: true
      },
      {
        id: 'express',
        name: 'Express Shipping',
        description: '2-3 business days',
        price: 19.99,
        estimatedDays: '2-3',
        carrier: 'Express Courier',
        trackingAvailable: true
      },
      {
        id: 'overnight',
        name: 'Overnight Shipping',
        description: 'Next business day',
        price: 39.99,
        estimatedDays: '1',
        carrier: 'Premium Express',
        trackingAvailable: true
      }
    ];

    // Adjust prices based on weight and destination
    const weightMultiplier = Math.max(1, Math.ceil(parseFloat(weight)));
    const internationalMultiplier = country !== 'US' ? 1.5 : 1;

    const adjustedMethods = shippingMethods.map(method => ({
      ...method,
      price: Math.round((method.price * weightMultiplier * internationalMultiplier) * 100) / 100
    }));

    // Add free shipping option if order value is high enough
    const freeShippingThreshold = parseFloat(process.env.FREE_SHIPPING_THRESHOLD) || 100;
    if (parseFloat(value) >= freeShippingThreshold) {
      adjustedMethods.unshift({
        id: 'free',
        name: 'Free Shipping',
        description: '7-10 business days',
        price: 0,
        estimatedDays: '7-10',
        carrier: 'Standard Post',
        trackingAvailable: true
      });
    }

    res.status(200).json({
      success: true,
      data: {
        shippingMethods: adjustedMethods,
        freeShippingThreshold,
        destination: country
      }
    });
  } catch (error) {
    logger.error('Get shipping methods error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/shipping/calculate:
 *   post:
 *     summary: Calculate shipping cost
 *     tags: [Shipping]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - items
 *               - destination
 *             properties:
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     weight:
 *                       type: number
 *                     dimensions:
 *                       type: object
 *                     quantity:
 *                       type: number
 *               destination:
 *                 type: object
 *                 properties:
 *                   country:
 *                     type: string
 *                   state:
 *                     type: string
 *                   city:
 *                     type: string
 *                   postalCode:
 *                     type: string
 *               shippingMethod:
 *                 type: string
 *     responses:
 *       200:
 *         description: Shipping cost calculated successfully
 */
router.post('/calculate', [
  body('items')
    .isArray({ min: 1 })
    .withMessage('Items array is required'),
  body('destination.country')
    .notEmpty()
    .withMessage('Destination country is required'),
  body('destination.postalCode')
    .notEmpty()
    .withMessage('Destination postal code is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    const { items, destination, shippingMethod = 'standard' } = req.body;

    // Calculate total weight and dimensions
    let totalWeight = 0;
    let totalVolume = 0;

    items.forEach(item => {
      const weight = (item.weight || 0.5) * (item.quantity || 1);
      totalWeight += weight;

      if (item.dimensions) {
        const volume = (item.dimensions.length || 10) * 
                      (item.dimensions.width || 10) * 
                      (item.dimensions.height || 10) * 
                      (item.quantity || 1);
        totalVolume += volume;
      }
    });

    // Base shipping rates
    const baseRates = {
      standard: 9.99,
      express: 19.99,
      overnight: 39.99,
      free: 0
    };

    let shippingCost = baseRates[shippingMethod] || baseRates.standard;

    // Apply weight-based pricing
    if (totalWeight > 1) {
      shippingCost += (totalWeight - 1) * 2.50;
    }

    // Apply international shipping surcharge
    if (destination.country !== 'US') {
      shippingCost *= 1.5;
    }

    // Apply volume-based pricing for large packages
    if (totalVolume > 1000) { // cm³
      shippingCost += Math.ceil(totalVolume / 1000) * 5;
    }

    // Round to 2 decimal places
    shippingCost = Math.round(shippingCost * 100) / 100;

    // Estimate delivery date
    const deliveryDays = {
      standard: 7,
      express: 3,
      overnight: 1,
      free: 10
    };

    const estimatedDelivery = new Date();
    estimatedDelivery.setDate(estimatedDelivery.getDate() + (deliveryDays[shippingMethod] || 7));

    res.status(200).json({
      success: true,
      data: {
        shippingCost,
        shippingMethod,
        totalWeight,
        totalVolume,
        estimatedDelivery: estimatedDelivery.toISOString().split('T')[0],
        destination,
        breakdown: {
          baseCost: baseRates[shippingMethod] || baseRates.standard,
          weightSurcharge: totalWeight > 1 ? (totalWeight - 1) * 2.50 : 0,
          internationalSurcharge: destination.country !== 'US' ? shippingCost * 0.5 : 0,
          volumeSurcharge: totalVolume > 1000 ? Math.ceil(totalVolume / 1000) * 5 : 0
        }
      }
    });
  } catch (error) {
    logger.error('Calculate shipping error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/shipping/track/{trackingNumber}:
 *   get:
 *     summary: Track shipment
 *     tags: [Shipping]
 *     parameters:
 *       - in: path
 *         name: trackingNumber
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Tracking information retrieved successfully
 *       404:
 *         description: Tracking number not found
 */
router.get('/track/:trackingNumber', async (req, res, next) => {
  try {
    const { trackingNumber } = req.params;

    // Simulate tracking information
    const trackingInfo = {
      trackingNumber,
      status: 'in_transit',
      carrier: 'Express Courier',
      estimatedDelivery: '2024-02-15',
      currentLocation: 'Distribution Center - New York',
      events: [
        {
          date: '2024-02-10T09:00:00Z',
          status: 'picked_up',
          location: 'Warehouse - Los Angeles',
          description: 'Package picked up from sender'
        },
        {
          date: '2024-02-11T14:30:00Z',
          status: 'in_transit',
          location: 'Sorting Facility - Phoenix',
          description: 'Package in transit'
        },
        {
          date: '2024-02-12T08:15:00Z',
          status: 'in_transit',
          location: 'Distribution Center - Denver',
          description: 'Package arrived at distribution center'
        },
        {
          date: '2024-02-13T11:45:00Z',
          status: 'in_transit',
          location: 'Distribution Center - New York',
          description: 'Package out for delivery'
        }
      ]
    };

    res.status(200).json({
      success: true,
      data: {
        tracking: trackingInfo
      }
    });
  } catch (error) {
    logger.error('Track shipment error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/shipping/zones:
 *   get:
 *     summary: Get shipping zones
 *     tags: [Shipping]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Shipping zones retrieved successfully
 */
router.get('/zones', protect, restrictTo('admin', 'merchant'), async (req, res, next) => {
  try {
    const shippingZones = [
      {
        id: 'domestic',
        name: 'Domestic',
        countries: ['US'],
        rates: {
          standard: 9.99,
          express: 19.99,
          overnight: 39.99
        }
      },
      {
        id: 'north_america',
        name: 'North America',
        countries: ['CA', 'MX'],
        rates: {
          standard: 14.99,
          express: 29.99,
          overnight: 59.99
        }
      },
      {
        id: 'europe',
        name: 'Europe',
        countries: ['GB', 'DE', 'FR', 'IT', 'ES'],
        rates: {
          standard: 24.99,
          express: 49.99
        }
      },
      {
        id: 'asia_pacific',
        name: 'Asia Pacific',
        countries: ['JP', 'AU', 'SG', 'HK'],
        rates: {
          standard: 29.99,
          express: 59.99
        }
      },
      {
        id: 'rest_of_world',
        name: 'Rest of World',
        countries: ['*'],
        rates: {
          standard: 34.99
        }
      }
    ];

    res.status(200).json({
      success: true,
      data: {
        shippingZones
      }
    });
  } catch (error) {
    logger.error('Get shipping zones error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/shipping/carriers:
 *   get:
 *     summary: Get available carriers
 *     tags: [Shipping]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Carriers retrieved successfully
 */
router.get('/carriers', protect, restrictTo('admin', 'merchant'), async (req, res, next) => {
  try {
    const carriers = [
      {
        id: 'usps',
        name: 'USPS',
        description: 'United States Postal Service',
        trackingUrl: 'https://tools.usps.com/go/TrackConfirmAction?tLabels={trackingNumber}',
        services: ['First-Class', 'Priority', 'Express']
      },
      {
        id: 'ups',
        name: 'UPS',
        description: 'United Parcel Service',
        trackingUrl: 'https://www.ups.com/track?tracknum={trackingNumber}',
        services: ['Ground', '3 Day Select', '2nd Day Air', 'Next Day Air']
      },
      {
        id: 'fedex',
        name: 'FedEx',
        description: 'Federal Express',
        trackingUrl: 'https://www.fedex.com/fedextrack/?trknbr={trackingNumber}',
        services: ['Ground', 'Express Saver', '2Day', 'Overnight']
      },
      {
        id: 'dhl',
        name: 'DHL',
        description: 'DHL Express',
        trackingUrl: 'https://www.dhl.com/en/express/tracking.html?AWB={trackingNumber}',
        services: ['Express Worldwide', 'Express 12:00', 'Express 9:00']
      }
    ];

    res.status(200).json({
      success: true,
      data: {
        carriers
      }
    });
  } catch (error) {
    logger.error('Get carriers error:', error);
    next(error);
  }
});

module.exports = router;
