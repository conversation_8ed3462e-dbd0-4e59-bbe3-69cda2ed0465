const express = require('express');
const { body, validationResult } = require('express-validator');
const { Cart, Product } = require('../models');
const { AppError } = require('../middleware/errorHandler');
const { protect, optionalAuth } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Helper function to get or create cart
const getOrCreateCart = async (userId, sessionId) => {
  let cart = await Cart.findByUserOrSession(userId, sessionId);
  
  if (!cart) {
    cart = await Cart.create({
      user: userId,
      sessionId: sessionId,
      items: []
    });
  }
  
  return cart;
};

/**
 * @swagger
 * /api/cart:
 *   get:
 *     summary: Get current user's cart
 *     tags: [Cart]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: x-session-id
 *         schema:
 *           type: string
 *         description: Session ID for guest users
 *     responses:
 *       200:
 *         description: Cart retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     cart:
 *                       $ref: '#/components/schemas/Cart'
 */
router.get('/', optionalAuth, async (req, res, next) => {
  try {
    const userId = req.user?.id;
    const sessionId = req.headers['x-session-id'];

    if (!userId && !sessionId) {
      return next(new AppError('User ID or session ID is required', 400));
    }

    const cart = await getOrCreateCart(userId, sessionId);

    // Filter out inactive products
    cart.items = cart.items.filter(item => 
      item.product && item.product.status === 'active'
    );

    // Save cart if items were filtered
    if (cart.isModified('items')) {
      await cart.save();
    }

    res.status(200).json({
      success: true,
      data: {
        cart
      }
    });
  } catch (error) {
    logger.error('Get cart error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/cart/items:
 *   post:
 *     summary: Add item to cart
 *     tags: [Cart]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: x-session-id
 *         schema:
 *           type: string
 *         description: Session ID for guest users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - productId
 *               - quantity
 *             properties:
 *               productId:
 *                 type: string
 *                 description: Product ID
 *               variantId:
 *                 type: string
 *                 description: Product variant ID (optional)
 *               quantity:
 *                 type: number
 *                 minimum: 1
 *                 description: Quantity to add
 *               options:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     value:
 *                       type: string
 *                 description: Product options
 *     responses:
 *       200:
 *         description: Item added to cart successfully
 *       400:
 *         description: Validation error or product not available
 *       404:
 *         description: Product not found
 */
router.post('/items', optionalAuth, [
  body('productId')
    .isMongoId()
    .withMessage('Valid product ID is required'),
  body('variantId')
    .optional()
    .isMongoId()
    .withMessage('Valid variant ID is required'),
  body('quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be at least 1'),
  body('options')
    .optional()
    .isArray()
    .withMessage('Options must be an array')
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    const userId = req.user?.id;
    const sessionId = req.headers['x-session-id'];
    const { productId, variantId, quantity, options = [] } = req.body;

    if (!userId && !sessionId) {
      return next(new AppError('User ID or session ID is required', 400));
    }

    // Get product
    const product = await Product.findById(productId);
    if (!product) {
      return next(new AppError('Product not found', 404));
    }

    if (product.status !== 'active') {
      return next(new AppError('Product is not available', 400));
    }

    // Check inventory
    let availableQuantity = product.inventory.quantity;
    let price = product.price;

    if (variantId) {
      const variant = product.variants.id(variantId);
      if (!variant) {
        return next(new AppError('Product variant not found', 404));
      }
      if (!variant.active) {
        return next(new AppError('Product variant is not available', 400));
      }
      availableQuantity = variant.inventory.quantity;
      price = variant.price || product.price;
    }

    if (product.inventory.trackQuantity && availableQuantity < quantity) {
      if (!product.inventory.allowBackorder) {
        return next(new AppError(`Only ${availableQuantity} items available in stock`, 400));
      }
    }

    // Get or create cart
    const cart = await getOrCreateCart(userId, sessionId);

    // Add item to cart
    await cart.addItem(productId, variantId, quantity, price, options);
    await cart.populate('items.product', 'name price images inventory status');

    res.status(200).json({
      success: true,
      message: 'Item added to cart successfully',
      data: {
        cart
      }
    });
  } catch (error) {
    logger.error('Add to cart error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/cart/items/{itemId}:
 *   patch:
 *     summary: Update cart item quantity
 *     tags: [Cart]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: itemId
 *         required: true
 *         schema:
 *           type: string
 *         description: Cart item ID
 *       - in: header
 *         name: x-session-id
 *         schema:
 *           type: string
 *         description: Session ID for guest users
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - quantity
 *             properties:
 *               quantity:
 *                 type: number
 *                 minimum: 1
 *                 description: New quantity
 *     responses:
 *       200:
 *         description: Cart item updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Cart or item not found
 */
router.patch('/items/:itemId', optionalAuth, [
  body('quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be at least 1')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    const userId = req.user?.id;
    const sessionId = req.headers['x-session-id'];
    const { quantity } = req.body;

    if (!userId && !sessionId) {
      return next(new AppError('User ID or session ID is required', 400));
    }

    const cart = await Cart.findByUserOrSession(userId, sessionId);
    if (!cart) {
      return next(new AppError('Cart not found', 404));
    }

    const item = cart.items.id(req.params.itemId);
    if (!item) {
      return next(new AppError('Cart item not found', 404));
    }

    // Check inventory for the new quantity
    const product = await Product.findById(item.product);
    if (product && product.inventory.trackQuantity) {
      let availableQuantity = product.inventory.quantity;
      
      if (item.variant) {
        const variant = product.variants.id(item.variant);
        if (variant) {
          availableQuantity = variant.inventory.quantity;
        }
      }

      if (availableQuantity < quantity && !product.inventory.allowBackorder) {
        return next(new AppError(`Only ${availableQuantity} items available in stock`, 400));
      }
    }

    await cart.updateItemQuantity(req.params.itemId, quantity);
    await cart.populate('items.product', 'name price images inventory status');

    res.status(200).json({
      success: true,
      message: 'Cart item updated successfully',
      data: {
        cart
      }
    });
  } catch (error) {
    logger.error('Update cart item error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/cart/items/{itemId}:
 *   delete:
 *     summary: Remove item from cart
 *     tags: [Cart]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: itemId
 *         required: true
 *         schema:
 *           type: string
 *         description: Cart item ID
 *       - in: header
 *         name: x-session-id
 *         schema:
 *           type: string
 *         description: Session ID for guest users
 *     responses:
 *       200:
 *         description: Item removed from cart successfully
 *       404:
 *         description: Cart or item not found
 */
router.delete('/items/:itemId', optionalAuth, async (req, res, next) => {
  try {
    const userId = req.user?.id;
    const sessionId = req.headers['x-session-id'];

    if (!userId && !sessionId) {
      return next(new AppError('User ID or session ID is required', 400));
    }

    const cart = await Cart.findByUserOrSession(userId, sessionId);
    if (!cart) {
      return next(new AppError('Cart not found', 404));
    }

    const item = cart.items.id(req.params.itemId);
    if (!item) {
      return next(new AppError('Cart item not found', 404));
    }

    await cart.removeItem(req.params.itemId);
    await cart.populate('items.product', 'name price images inventory status');

    res.status(200).json({
      success: true,
      message: 'Item removed from cart successfully',
      data: {
        cart
      }
    });
  } catch (error) {
    logger.error('Remove cart item error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/cart/clear:
 *   delete:
 *     summary: Clear all items from cart
 *     tags: [Cart]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: x-session-id
 *         schema:
 *           type: string
 *         description: Session ID for guest users
 *     responses:
 *       200:
 *         description: Cart cleared successfully
 *       404:
 *         description: Cart not found
 */
router.delete('/clear', optionalAuth, async (req, res, next) => {
  try {
    const userId = req.user?.id;
    const sessionId = req.headers['x-session-id'];

    if (!userId && !sessionId) {
      return next(new AppError('User ID or session ID is required', 400));
    }

    const cart = await Cart.findByUserOrSession(userId, sessionId);
    if (!cart) {
      return next(new AppError('Cart not found', 404));
    }

    await cart.clear();

    res.status(200).json({
      success: true,
      message: 'Cart cleared successfully',
      data: {
        cart
      }
    });
  } catch (error) {
    logger.error('Clear cart error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/cart/merge:
 *   post:
 *     summary: Merge guest cart with user cart (when user logs in)
 *     tags: [Cart]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - guestSessionId
 *             properties:
 *               guestSessionId:
 *                 type: string
 *                 description: Guest session ID
 *     responses:
 *       200:
 *         description: Carts merged successfully
 *       404:
 *         description: Guest cart not found
 */
router.post('/merge', protect, [
  body('guestSessionId')
    .notEmpty()
    .withMessage('Guest session ID is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    const { guestSessionId } = req.body;
    const userId = req.user.id;

    // Get guest cart
    const guestCart = await Cart.findOne({ 
      sessionId: guestSessionId, 
      status: 'active' 
    }).populate('items.product');

    if (!guestCart || guestCart.items.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No guest cart to merge',
        data: {
          cart: await getOrCreateCart(userId, null)
        }
      });
    }

    // Get or create user cart
    let userCart = await Cart.findOne({ 
      user: userId, 
      status: 'active' 
    }).populate('items.product');

    if (!userCart) {
      // Convert guest cart to user cart
      guestCart.user = userId;
      guestCart.sessionId = undefined;
      await guestCart.save();
      userCart = guestCart;
    } else {
      // Merge carts
      userCart = await Cart.mergeCarts(guestCart, userCart);
    }

    await userCart.populate('items.product', 'name price images inventory status');

    res.status(200).json({
      success: true,
      message: 'Carts merged successfully',
      data: {
        cart: userCart
      }
    });
  } catch (error) {
    logger.error('Merge cart error:', error);
    next(error);
  }
});

module.exports = router;
