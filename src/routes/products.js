const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { Product, Category, User, sequelize } = require('../models');
const { AppError } = require('../middleware/errorHandler');
const { protect, restrictTo, optionalAuth } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Helper function for building search query
const buildSearchQuery = (req) => {
  const { Op } = require('sequelize');
  const where = {};

  // Text search
  if (req.query.search) {
    where[Op.or] = [
      { name: { [Op.like]: `%${req.query.search}%` } },
      { description: { [Op.like]: `%${req.query.search}%` } }
    ];
  }

  // Category filter
  if (req.query.category) {
    where.categoryId = req.query.category;
  }

  // Brand filter
  if (req.query.brand) {
    where.brandId = req.query.brand;
  }

  // Price range filter
  if (req.query.minPrice || req.query.maxPrice) {
    where.price = {};
    if (req.query.minPrice) where.price[Op.gte] = parseFloat(req.query.minPrice);
    if (req.query.maxPrice) where.price[Op.lte] = parseFloat(req.query.maxPrice);
  }

  // Status filter (only active products for public)
  if (!req.user || req.user.role === 'customer') {
    where.status = 'active';
  } else if (req.query.status) {
    where.status = req.query.status;
  }

  // Merchant filter (for admin)
  if (req.query.merchant && req.user && req.user.role === 'admin') {
    where.merchantId = req.query.merchant;
  }

  // Featured filter
  if (req.query.featured === 'true') {
    where.featured = true;
  }

  // Tags filter
  if (req.query.tags) {
    const tags = req.query.tags.split(',');
    where.tags = { [Op.overlap]: tags };
  }

  return where;
};

/**
 * @swagger
 * /api/products:
 *   get:
 *     summary: Get all products with filtering and pagination
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of products per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Category ID
 *       - in: query
 *         name: brand
 *         schema:
 *           type: string
 *         description: Brand ID
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: Minimum price
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: Maximum price
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [newest, oldest, price_low, price_high, popular, rating]
 *           default: newest
 *         description: Sort order
 *       - in: query
 *         name: featured
 *         schema:
 *           type: boolean
 *         description: Filter featured products
 *     responses:
 *       200:
 *         description: Products retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     products:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Product'
 *                     pagination:
 *                       type: object
 */
router.get('/', optionalAuth, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('minPrice').optional().isFloat({ min: 0 }).withMessage('Minimum price must be a positive number'),
  query('maxPrice').optional().isFloat({ min: 0 }).withMessage('Maximum price must be a positive number')
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Build search query
    const where = buildSearchQuery(req);

    // Build sort query
    let order = [];
    switch (req.query.sort) {
      case 'oldest':
        order = [['createdAt', 'ASC']];
        break;
      case 'price_low':
        order = [['price', 'ASC']];
        break;
      case 'price_high':
        order = [['price', 'DESC']];
        break;
      case 'popular':
        order = [['salesCount', 'DESC']];
        break;
      case 'rating':
        order = [[sequelize.literal("JSON_EXTRACT(ratings, '$.average')"), 'DESC']];
        break;
      default:
        order = [['createdAt', 'DESC']];
    }

    // Execute query
    const { count, rows: products } = await Product.findAndCountAll({
      where,
      include: [
        { model: Category, as: 'category', attributes: ['id', 'name', 'slug'] },
        { model: User, as: 'merchant', attributes: ['id', 'name'] }
      ],
      order,
      offset: skip,
      limit
    });

    const total = count;

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.status(200).json({
      success: true,
      data: {
        products,
        pagination: {
          currentPage: page,
          totalPages,
          totalProducts: total,
          hasNextPage,
          hasPrevPage,
          limit
        }
      }
    });
  } catch (error) {
    logger.error('Get products error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/products/{id}:
 *   get:
 *     summary: Get product by ID
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Product ID
 *     responses:
 *       200:
 *         description: Product retrieved successfully
 *       404:
 *         description: Product not found
 */
router.get('/:id', optionalAuth, async (req, res, next) => {
  try {
    const product = await Product.findByPk(req.params.id, {
      include: [
        { model: Category, as: 'category', attributes: ['id', 'name', 'slug'] },
        { model: User, as: 'merchant', attributes: ['id', 'name', 'email'] }
      ]
    });

    if (!product) {
      return next(new AppError('Product not found', 404));
    }

    // Check if user can view this product
    if (product.status !== 'active' && (!req.user || (req.user.role === 'customer'))) {
      return next(new AppError('Product not found', 404));
    }

    // Increment views count
    await product.increment('viewsCount');

    res.status(200).json({
      success: true,
      data: {
        product
      }
    });
  } catch (error) {
    logger.error('Get product error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/products:
 *   post:
 *     summary: Create a new product
 *     tags: [Products]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *               - price
 *               - category
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               shortDescription:
 *                 type: string
 *               price:
 *                 type: number
 *               comparePrice:
 *                 type: number
 *               category:
 *                 type: string
 *               brand:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               images:
 *                 type: array
 *                 items:
 *                   type: object
 *               inventory:
 *                 type: object
 *               status:
 *                 type: string
 *                 enum: [draft, active, archived]
 *     responses:
 *       201:
 *         description: Product created successfully
 *       400:
 *         description: Validation error
 *       403:
 *         description: Not authorized
 */
router.post('/', protect, restrictTo('merchant', 'admin'), [
  body('name')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Product name must be between 2 and 200 characters'),
  body('description')
    .trim()
    .isLength({ min: 10 })
    .withMessage('Description must be at least 10 characters'),
  body('price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('comparePrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Compare price must be a positive number'),
  body('category')
    .isMongoId()
    .withMessage('Please provide a valid category ID'),
  body('brand')
    .optional()
    .isMongoId()
    .withMessage('Please provide a valid brand ID')
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    // Verify category exists
    const category = await Category.findByPk(req.body.categoryId || req.body.category);
    if (!category) {
      return next(new AppError('Category not found', 404));
    }

    // Set merchant to current user (unless admin creating for another merchant)
    const productData = { ...req.body };
    if (req.user.role === 'merchant') {
      productData.merchantId = req.user.id;
    } else if (!productData.merchantId) {
      productData.merchantId = req.user.id;
    }

    // Ensure categoryId is set
    productData.categoryId = req.body.categoryId || req.body.category;

    const product = await Product.create(productData);

    // Reload with associations
    await product.reload({
      include: [
        { model: Category, as: 'category', attributes: ['id', 'name', 'slug'] },
        { model: User, as: 'merchant', attributes: ['id', 'name'] }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      data: {
        product
      }
    });
  } catch (error) {
    logger.error('Create product error:', error);
    next(error);
  }
});

module.exports = router;
