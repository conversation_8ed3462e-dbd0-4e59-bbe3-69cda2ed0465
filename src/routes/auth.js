const express = require('express');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const { User, Store } = require('../models');
const { AppError } = require('../middleware/errorHandler');
const { protect, restrictTo } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Auth-specific rate limiting - more generous for development
const authLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 50, // limit each IP to 50 auth requests per minute (increased)
  message: {
    error: 'Too many authentication attempts, please try again later.',
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for localhost in development
    return req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === '::ffff:127.0.0.1';
  }
});

// Helper function to create JWT token
const signToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE || '7d'
  });
};

// Helper function to create and send token response
const createSendToken = (user, statusCode, res, message = 'Success') => {
  const token = signToken(user.id);
  
  const cookieOptions = {
    expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  };

  res.cookie('jwt', token, cookieOptions);

  // Remove password from output
  user.password = undefined;

  res.status(statusCode).json({
    success: true,
    message,
    token,
    data: {
      user
    }
  });
};

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - password
 *               - passwordConfirm
 *             properties:
 *               name:
 *                 type: string
 *                 example: John Doe
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 minLength: 8
 *                 example: password123
 *               passwordConfirm:
 *                 type: string
 *                 example: password123
 *               phone:
 *                 type: string
 *                 example: +1234567890
 *               role:
 *                 type: string
 *                 enum: [customer, merchant]
 *                 default: customer
 *     responses:
 *       201:
 *         description: User registered successfully
 *       400:
 *         description: Validation error
 *       409:
 *         description: User already exists
 */
router.post('/register', authLimiter, [
  body('name')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  body('passwordConfirm')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Password confirmation does not match password');
      }
      return true;
    }),
  body('phone')
    .notEmpty()
    .withMessage('Phone number is required')
    .matches(/^\+966[0-9]{9}$/)
    .withMessage('Phone number must start with +966 and be followed by 9 digits (e.g., +966501234567)'),
  body('role')
    .optional()
    .isIn(['customer', 'merchant', 'admin'])
    .withMessage('Role must be either customer, merchant, or admin'),
  // Store fields for admin/merchant registration
  body('storeName')
    .if(body('role').isIn(['admin', 'merchant']))
    .notEmpty()
    .withMessage('Store name is required for admin/merchant registration'),
  body('storeDomain')
    .if(body('role').isIn(['admin', 'merchant']))
    .notEmpty()
    .withMessage('Store domain is required for admin/merchant registration')
    .matches(/^[a-z0-9-]+$/)
    .withMessage('Domain can only contain lowercase letters, numbers, and hyphens')
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    const { name, email, password, phone, role, storeName, storeDomain, storeDescription } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return next(new AppError('An account with this email already exists. Please use a different email or try logging in.', 409));
    }

    // Check if phone number already exists
    if (phone) {
      const existingPhone = await User.findOne({ where: { phone } });
      if (existingPhone) {
        return next(new AppError('This phone number is already registered. Please use a different phone number.', 409));
      }
    }

    // Check if store domain already exists (for admin/merchant)
    if ((role === 'admin' || role === 'merchant') && storeDomain) {
      const existingStore = await Store.findOne({ where: { domain: storeDomain.toLowerCase() } });
      if (existingStore) {
        return next(new AppError('This store domain is already taken. Please choose a different domain name.', 409));
      }
    }

    // Create new user
    const newUser = await User.create({
      name,
      email,
      password,
      phone,
      role: role || 'customer'
    });

    // Create store for admin/merchant
    if ((role === 'admin' || role === 'merchant') && storeName && storeDomain) {
      await Store.create({
        name: storeName,
        description: storeDescription || '',
        ownerId: newUser.id,
        domain: storeDomain.toLowerCase(),
        isActive: true
      });
    }

    // Generate email verification token
    const verifyToken = newUser.createEmailVerificationToken();
    await newUser.save({ validateBeforeSave: false });

    // TODO: Send verification email
    logger.info(`Email verification token for ${email}: ${verifyToken}`);

    createSendToken(newUser, 201, res, 'User registered successfully. Please check your email to verify your account.');
  } catch (error) {
    logger.error('Registration error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Login user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 example: password123
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 */
router.post('/login', authLimiter, [
  // Custom validation for email or phone
  body('email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email'),
  body('phone')
    .optional()
    .matches(/^\+966[0-9]{9}$/)
    .withMessage('Please provide a valid Saudi phone number'),
  body('emailOrPhone')
    .optional()
    .custom((value) => {
      // Check if it's email or phone
      if (value && value.includes('@')) {
        // Validate as email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          throw new Error('Please provide a valid email');
        }
      } else if (value && value.startsWith('+966')) {
        // Validate as Saudi phone
        const phoneRegex = /^\+966[0-9]{9}$/;
        if (!phoneRegex.test(value)) {
          throw new Error('Please provide a valid Saudi phone number');
        }
      }
      return true;
    }),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    const { email, phone, emailOrPhone, password } = req.body;

    let user;
    let loginIdentifier;

    // Determine login method and find user
    if (emailOrPhone) {
      // Login with emailOrPhone field
      if (emailOrPhone.includes('@')) {
        // It's an email
        user = await User.findOne({ where: { email: emailOrPhone } });
        loginIdentifier = emailOrPhone;
      } else if (emailOrPhone.startsWith('+966')) {
        // It's a phone number
        user = await User.findOne({ where: { phone: emailOrPhone } });
        loginIdentifier = emailOrPhone;
      }
    } else if (email) {
      // Login with email field
      user = await User.findOne({ where: { email } });
      loginIdentifier = email;
    } else if (phone) {
      // Login with phone field
      user = await User.findOne({ where: { phone } });
      loginIdentifier = phone;
    }

    if (!user || !(await user.correctPassword(password))) {
      return next(new AppError('Incorrect email/phone or password', 401));
    }

    // Check if user is active
    if (!user.active) {
      return next(new AppError('Your account has been deactivated. Please contact support.', 401));
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    createSendToken(user, 200, res, 'Login successful');
  } catch (error) {
    logger.error('Login error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/auth/logout:
 *   post:
 *     summary: Logout user
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Logout successful
 */
router.post('/logout', (req, res) => {
  res.cookie('jwt', 'loggedout', {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true
  });
  
  res.status(200).json({
    success: true,
    message: 'Logout successful'
  });
});

/**
 * @swagger
 * /api/auth/me:
 *   get:
 *     summary: Get current user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *       401:
 *         description: Not authenticated
 */
router.get('/me', protect, async (req, res, next) => {
  try {
    const user = await User.findByPk(req.user.id);
    
    res.status(200).json({
      success: true,
      data: {
        user
      }
    });
  } catch (error) {
    logger.error('Get profile error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/auth/update-profile:
 *   patch:
 *     summary: Update user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               phone:
 *                 type: string
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *               gender:
 *                 type: string
 *                 enum: [male, female, other]
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *       400:
 *         description: Validation error
 */
router.patch('/update-profile', protect, [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
  body('phone')
    .optional()
    .matches(/^\+?[1-9]\d{1,14}$/)
    .withMessage('Please provide a valid phone number'),
  body('dateOfBirth')
    .optional()
    .isISO8601()
    .withMessage('Please provide a valid date'),
  body('gender')
    .optional()
    .isIn(['male', 'female', 'other'])
    .withMessage('Gender must be male, female, or other')
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    const allowedFields = ['name', 'phone', 'dateOfBirth', 'gender'];
    const updates = {};
    
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        updates[field] = req.body[field];
      }
    });

    const user = await User.findByPk(req.user.id);
    if (!user) {
      return next(new AppError('User not found', 404));
    }

    await user.update(updates);

    res.status(200).json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user
      }
    });
  } catch (error) {
    logger.error('Update profile error:', error);
    next(error);
  }
});

module.exports = router;
