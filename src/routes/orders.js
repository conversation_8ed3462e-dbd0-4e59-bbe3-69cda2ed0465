const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Order = require('../models/Order');
const Product = require('../models/Product');
const Cart = require('../models/Cart');
const { AppError } = require('../middleware/errorHandler');
const { protect, restrictTo, checkOwnership } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @swagger
 * /api/orders:
 *   get:
 *     summary: Get all orders (with filtering)
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, confirmed, processing, shipped, delivered, cancelled, refunded]
 *       - in: query
 *         name: paymentStatus
 *         schema:
 *           type: string
 *           enum: [pending, paid, failed, refunded, partially_refunded]
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 */
router.get('/', protect, async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Build query based on user role
    let query = {};
    
    if (req.user.role === 'customer') {
      query.customer = req.user.id;
    } else if (req.user.role === 'merchant') {
      query['items.merchant'] = req.user.id;
    }
    // Admin can see all orders

    // Apply filters
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    if (req.query.paymentStatus) {
      query.paymentStatus = req.query.paymentStatus;
    }
    
    if (req.query.startDate || req.query.endDate) {
      query.createdAt = {};
      if (req.query.startDate) {
        query.createdAt.$gte = new Date(req.query.startDate);
      }
      if (req.query.endDate) {
        query.createdAt.$lte = new Date(req.query.endDate);
      }
    }

    const orders = await Order.find(query)
      .populate('customer', 'name email')
      .populate('items.product', 'name images')
      .populate('items.merchant', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Order.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: page,
          totalPages,
          totalOrders: total,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit
        }
      }
    });
  } catch (error) {
    logger.error('Get orders error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/orders/{id}:
 *   get:
 *     summary: Get order by ID
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Order retrieved successfully
 *       404:
 *         description: Order not found
 */
router.get('/:id', protect, async (req, res, next) => {
  try {
    let query = { _id: req.params.id };
    
    // Restrict access based on user role
    if (req.user.role === 'customer') {
      query.customer = req.user.id;
    } else if (req.user.role === 'merchant') {
      query['items.merchant'] = req.user.id;
    }

    const order = await Order.findOne(query)
      .populate('customer', 'name email phone')
      .populate('items.product', 'name images sku')
      .populate('items.merchant', 'name email');

    if (!order) {
      return next(new AppError('Order not found', 404));
    }

    res.status(200).json({
      success: true,
      data: {
        order
      }
    });
  } catch (error) {
    logger.error('Get order error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/orders:
 *   post:
 *     summary: Create a new order
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - items
 *               - shippingAddress
 *               - paymentMethod
 *             properties:
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     product:
 *                       type: string
 *                     variant:
 *                       type: string
 *                     quantity:
 *                       type: number
 *                     price:
 *                       type: number
 *               shippingAddress:
 *                 type: object
 *               billingAddress:
 *                 type: object
 *               paymentMethod:
 *                 type: object
 *               shippingMethod:
 *                 type: object
 *               coupon:
 *                 type: object
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Order created successfully
 *       400:
 *         description: Validation error
 */
router.post('/', protect, restrictTo('customer', 'admin'), [
  body('items')
    .isArray({ min: 1 })
    .withMessage('Order must contain at least one item'),
  body('items.*.product')
    .isMongoId()
    .withMessage('Product ID is required'),
  body('items.*.quantity')
    .isInt({ min: 1 })
    .withMessage('Quantity must be at least 1'),
  body('items.*.price')
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  body('shippingAddress.firstName')
    .trim()
    .notEmpty()
    .withMessage('First name is required'),
  body('shippingAddress.lastName')
    .trim()
    .notEmpty()
    .withMessage('Last name is required'),
  body('shippingAddress.address1')
    .trim()
    .notEmpty()
    .withMessage('Address is required'),
  body('shippingAddress.city')
    .trim()
    .notEmpty()
    .withMessage('City is required'),
  body('shippingAddress.country')
    .trim()
    .notEmpty()
    .withMessage('Country is required'),
  body('paymentMethod.type')
    .isIn(['credit_card', 'debit_card', 'paypal', 'stripe', 'cash_on_delivery', 'bank_transfer'])
    .withMessage('Invalid payment method')
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    const { items, shippingAddress, billingAddress, paymentMethod, shippingMethod, coupon, notes } = req.body;

    // Validate and process items
    const processedItems = [];
    let subtotal = 0;

    for (const item of items) {
      const product = await Product.findById(item.product);
      if (!product) {
        return next(new AppError(`Product ${item.product} not found`, 404));
      }

      if (product.status !== 'active') {
        return next(new AppError(`Product ${product.name} is not available`, 400));
      }

      // Check inventory
      if (product.inventory.trackQuantity && product.inventory.quantity < item.quantity) {
        if (!product.inventory.allowBackorder) {
          return next(new AppError(`Insufficient stock for ${product.name}`, 400));
        }
      }

      const itemTotal = item.price * item.quantity;
      subtotal += itemTotal;

      processedItems.push({
        product: product._id,
        variant: item.variant,
        name: product.name,
        sku: product.sku,
        image: product.mainImage,
        price: item.price,
        quantity: item.quantity,
        totalPrice: itemTotal,
        options: item.options || [],
        merchant: product.merchant
      });
    }

    // Calculate totals
    const taxRate = parseFloat(process.env.DEFAULT_TAX_RATE) || 0.15;
    const taxAmount = subtotal * taxRate;
    const shippingAmount = shippingMethod?.price || 0;
    let discountAmount = 0;

    // Apply coupon if provided
    if (coupon && coupon.code) {
      // TODO: Validate coupon and calculate discount
      discountAmount = coupon.discountAmount || 0;
    }

    const totalAmount = subtotal + taxAmount + shippingAmount - discountAmount;

    // Create order
    const orderData = {
      customer: req.user.id,
      items: processedItems,
      subtotal,
      taxAmount,
      shippingAmount,
      discountAmount,
      totalAmount,
      shippingAddress,
      billingAddress: billingAddress || shippingAddress,
      paymentMethod,
      shippingMethod,
      coupon,
      notes,
      source: 'api',
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    };

    const order = await Order.create(orderData);

    // Update product inventory
    for (const item of processedItems) {
      if (item.product.inventory.trackQuantity) {
        await Product.findByIdAndUpdate(item.product, {
          $inc: { 
            'inventory.quantity': -item.quantity,
            'salesCount': item.quantity
          }
        });
      }
    }

    // Clear user's cart if order was created from cart
    if (req.body.clearCart) {
      await Cart.findOneAndUpdate(
        { user: req.user.id, status: 'active' },
        { status: 'converted' }
      );
    }

    await order.populate('customer', 'name email');
    await order.populate('items.product', 'name images');

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: {
        order
      }
    });
  } catch (error) {
    logger.error('Create order error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/orders/{id}/status:
 *   patch:
 *     summary: Update order status
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [pending, confirmed, processing, shipped, delivered, cancelled, refunded]
 *               note:
 *                 type: string
 *               trackingNumber:
 *                 type: string
 *               cancellationReason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Order status updated successfully
 *       404:
 *         description: Order not found
 *       403:
 *         description: Not authorized to update this order
 */
router.patch('/:id/status', protect, restrictTo('merchant', 'admin'), [
  body('status')
    .isIn(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'])
    .withMessage('Invalid status')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    let query = { _id: req.params.id };
    
    // Merchants can only update orders containing their products
    if (req.user.role === 'merchant') {
      query['items.merchant'] = req.user.id;
    }

    const order = await Order.findOne(query);
    if (!order) {
      return next(new AppError('Order not found', 404));
    }

    const { status, note, trackingNumber, cancellationReason } = req.body;

    // Update order status
    order.status = status;
    
    if (note) {
      order.timeline.push({
        status,
        note,
        user: req.user.id,
        timestamp: new Date()
      });
    }

    if (trackingNumber && status === 'shipped') {
      order.shippingMethod.trackingNumber = trackingNumber;
    }

    if (cancellationReason && status === 'cancelled') {
      order.cancellationReason = cancellationReason;
    }

    await order.save();

    res.status(200).json({
      success: true,
      message: 'Order status updated successfully',
      data: {
        order
      }
    });
  } catch (error) {
    logger.error('Update order status error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/orders/{id}/cancel:
 *   patch:
 *     summary: Cancel an order
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Order cancelled successfully
 *       400:
 *         description: Order cannot be cancelled
 *       404:
 *         description: Order not found
 */
router.patch('/:id/cancel', protect, async (req, res, next) => {
  try {
    let query = { _id: req.params.id };
    
    // Customers can only cancel their own orders
    if (req.user.role === 'customer') {
      query.customer = req.user.id;
    } else if (req.user.role === 'merchant') {
      query['items.merchant'] = req.user.id;
    }

    const order = await Order.findOne(query);
    if (!order) {
      return next(new AppError('Order not found', 404));
    }

    // Check if order can be cancelled
    if (!['pending', 'confirmed'].includes(order.status)) {
      return next(new AppError('Order cannot be cancelled at this stage', 400));
    }

    // Update order
    order.status = 'cancelled';
    order.cancellationReason = req.body.reason || 'Cancelled by customer';
    order.timeline.push({
      status: 'cancelled',
      note: order.cancellationReason,
      user: req.user.id,
      timestamp: new Date()
    });

    // Restore inventory
    for (const item of order.items) {
      await Product.findByIdAndUpdate(item.product, {
        $inc: { 
          'inventory.quantity': item.quantity,
          'salesCount': -item.quantity
        }
      });
    }

    await order.save();

    res.status(200).json({
      success: true,
      message: 'Order cancelled successfully',
      data: {
        order
      }
    });
  } catch (error) {
    logger.error('Cancel order error:', error);
    next(error);
  }
});

module.exports = router;
