const express = require('express');
const { body, validationResult } = require('express-validator');
const { Category, Product } = require('../models');
const { AppError } = require('../middleware/errorHandler');
const { protect, restrictTo, optionalAuth } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * @swagger
 * /api/categories:
 *   get:
 *     summary: Get all categories
 *     tags: [Categories]
 *     parameters:
 *       - in: query
 *         name: tree
 *         schema:
 *           type: boolean
 *         description: Return categories as tree structure
 *       - in: query
 *         name: featured
 *         schema:
 *           type: boolean
 *         description: Filter featured categories only
 *       - in: query
 *         name: parent
 *         schema:
 *           type: string
 *         description: Get children of specific parent category
 *       - in: query
 *         name: level
 *         schema:
 *           type: integer
 *         description: Filter by category level
 *     responses:
 *       200:
 *         description: Categories retrieved successfully
 */
router.get('/', optionalAuth, async (req, res, next) => {
  try {
    // Return tree structure if requested
    if (req.query.tree === 'true') {
      const tree = await Category.buildTree();
      return res.status(200).json({
        success: true,
        data: {
          categories: tree
        }
      });
    }

    // Return featured categories if requested
    if (req.query.featured === 'true') {
      const categories = await Category.getFeatured();
      return res.status(200).json({
        success: true,
        data: {
          categories
        }
      });
    }

    // Build query
    let where = { isActive: true };

    if (req.query.parent) {
      where.parentId = req.query.parent === 'null' ? null : req.query.parent;
    }

    if (req.query.level !== undefined) {
      where.level = parseInt(req.query.level);
    }

    const categories = await Category.findAll({
      where,
      include: [
        { model: Category, as: 'parent', attributes: ['id', 'name', 'slug'] }
      ],
      order: [['position', 'ASC'], ['name', 'ASC']]
    });

    // Add product count for each category
    const categoriesWithCount = await Promise.all(
      categories.map(async (category) => {
        const productCount = await Product.count({
          where: {
            categoryId: category.id,
            status: 'active'
          }
        });
        return {
          ...category.toJSON(),
          productCount
        };
      })
    );

    res.status(200).json({
      success: true,
      data: {
        categories: categoriesWithCount
      }
    });
  } catch (error) {
    logger.error('Get categories error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/categories/{id}:
 *   get:
 *     summary: Get category by ID
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: includeChildren
 *         schema:
 *           type: boolean
 *         description: Include child categories
 *       - in: query
 *         name: includePath
 *         schema:
 *           type: boolean
 *         description: Include category path
 *     responses:
 *       200:
 *         description: Category retrieved successfully
 *       404:
 *         description: Category not found
 */
router.get('/:id', optionalAuth, async (req, res, next) => {
  try {
    const category = await Category.findOne({ 
      _id: req.params.id, 
      isActive: true 
    }).populate('parent', 'name slug');

    if (!category) {
      return next(new AppError('Category not found', 404));
    }

    let result = category.toObject();

    // Include children if requested
    if (req.query.includeChildren === 'true') {
      const children = await Category.find({ 
        parent: category._id, 
        isActive: true 
      }).sort({ position: 1, name: 1 });
      result.children = children;
    }

    // Include path if requested
    if (req.query.includePath === 'true') {
      result.path = await category.getPath();
    }

    // Add product count
    result.productCount = await Product.countDocuments({ 
      category: category._id, 
      status: 'active' 
    });

    res.status(200).json({
      success: true,
      data: {
        category: result
      }
    });
  } catch (error) {
    logger.error('Get category error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/categories:
 *   post:
 *     summary: Create a new category
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               parent:
 *                 type: string
 *               image:
 *                 type: object
 *               icon:
 *                 type: string
 *               position:
 *                 type: number
 *               isActive:
 *                 type: boolean
 *               isFeatured:
 *                 type: boolean
 *               seo:
 *                 type: object
 *     responses:
 *       201:
 *         description: Category created successfully
 *       400:
 *         description: Validation error
 */
router.post('/', protect, restrictTo('admin', 'merchant'), [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Category name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  body('parent')
    .optional()
    .isMongoId()
    .withMessage('Parent must be a valid category ID')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    // Check if parent category exists
    if (req.body.parent) {
      const parentCategory = await Category.findById(req.body.parent);
      if (!parentCategory) {
        return next(new AppError('Parent category not found', 404));
      }
    }

    const category = await Category.create(req.body);

    // Reload with associations
    await category.reload({
      include: [
        { model: Category, as: 'parent', attributes: ['id', 'name', 'slug'] }
      ]
    });

    res.status(201).json({
      success: true,
      message: 'Category created successfully',
      data: {
        category
      }
    });
  } catch (error) {
    logger.error('Create category error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/categories/{id}:
 *   patch:
 *     summary: Update category
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               parent:
 *                 type: string
 *               image:
 *                 type: object
 *               icon:
 *                 type: string
 *               position:
 *                 type: number
 *               isActive:
 *                 type: boolean
 *               isFeatured:
 *                 type: boolean
 *               seo:
 *                 type: object
 *     responses:
 *       200:
 *         description: Category updated successfully
 *       404:
 *         description: Category not found
 */
router.patch('/:id', protect, restrictTo('admin', 'merchant'), [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Category name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  body('parent')
    .optional()
    .isMongoId()
    .withMessage('Parent must be a valid category ID')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError('Validation failed', 400, errors.array()));
    }

    const category = await Category.findById(req.params.id);
    if (!category) {
      return next(new AppError('Category not found', 404));
    }

    // Check if trying to set parent to itself or its descendant
    if (req.body.parent) {
      if (req.body.parent === req.params.id) {
        return next(new AppError('Category cannot be its own parent', 400));
      }

      const descendants = await category.getDescendants();
      const descendantIds = descendants.map(d => d._id.toString());
      if (descendantIds.includes(req.body.parent)) {
        return next(new AppError('Category cannot be moved to its descendant', 400));
      }

      const parentCategory = await Category.findById(req.body.parent);
      if (!parentCategory) {
        return next(new AppError('Parent category not found', 404));
      }
    }

    const updatedCategory = await Category.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('parent', 'name slug');

    res.status(200).json({
      success: true,
      message: 'Category updated successfully',
      data: {
        category: updatedCategory
      }
    });
  } catch (error) {
    logger.error('Update category error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/categories/{id}:
 *   delete:
 *     summary: Delete category
 *     tags: [Categories]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Category deleted successfully
 *       400:
 *         description: Category has products or subcategories
 *       404:
 *         description: Category not found
 */
router.delete('/:id', protect, restrictTo('admin'), async (req, res, next) => {
  try {
    const category = await Category.findById(req.params.id);
    if (!category) {
      return next(new AppError('Category not found', 404));
    }

    // Check if category has products
    const productCount = await Product.countDocuments({ category: category._id });
    if (productCount > 0) {
      return next(new AppError('Cannot delete category with products. Please move or delete products first.', 400));
    }

    // Check if category has subcategories
    const subcategoryCount = await Category.countDocuments({ parent: category._id });
    if (subcategoryCount > 0) {
      return next(new AppError('Cannot delete category with subcategories. Please move or delete subcategories first.', 400));
    }

    await Category.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'Category deleted successfully'
    });
  } catch (error) {
    logger.error('Delete category error:', error);
    next(error);
  }
});

/**
 * @swagger
 * /api/categories/{id}/products:
 *   get:
 *     summary: Get products in category
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [newest, oldest, price_low, price_high, popular, rating]
 *       - in: query
 *         name: includeSubcategories
 *         schema:
 *           type: boolean
 *         description: Include products from subcategories
 *     responses:
 *       200:
 *         description: Products retrieved successfully
 *       404:
 *         description: Category not found
 */
router.get('/:id/products', optionalAuth, async (req, res, next) => {
  try {
    const category = await Category.findOne({ 
      _id: req.params.id, 
      isActive: true 
    });

    if (!category) {
      return next(new AppError('Category not found', 404));
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Build category query
    let categoryQuery = { category: category._id };
    
    if (req.query.includeSubcategories === 'true') {
      const descendants = await category.getDescendants();
      const categoryIds = [category._id, ...descendants.map(d => d._id)];
      categoryQuery = { category: { $in: categoryIds } };
    }

    // Build product query
    let productQuery = {
      ...categoryQuery,
      status: 'active'
    };

    // Build sort query
    let sortQuery = {};
    switch (req.query.sort) {
      case 'oldest':
        sortQuery = { createdAt: 1 };
        break;
      case 'price_low':
        sortQuery = { price: 1 };
        break;
      case 'price_high':
        sortQuery = { price: -1 };
        break;
      case 'popular':
        sortQuery = { salesCount: -1 };
        break;
      case 'rating':
        sortQuery = { 'ratings.average': -1 };
        break;
      default:
        sortQuery = { createdAt: -1 };
    }

    const products = await Product.find(productQuery)
      .populate('category', 'name slug')
      .populate('brand', 'name')
      .sort(sortQuery)
      .skip(skip)
      .limit(limit)
      .lean();

    const total = await Product.countDocuments(productQuery);
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      data: {
        category: {
          _id: category._id,
          name: category.name,
          slug: category.slug
        },
        products,
        pagination: {
          currentPage: page,
          totalPages,
          totalProducts: total,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit
        }
      }
    });
  } catch (error) {
    logger.error('Get category products error:', error);
    next(error);
  }
});

module.exports = router;
