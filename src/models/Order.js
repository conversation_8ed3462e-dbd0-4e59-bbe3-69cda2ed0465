const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     Order:
 *       type: object
 *       required:
 *         - customerId
 *         - items
 *         - totalAmount
 *       properties:
 *         id:
 *           type: integer
 *           description: The auto-generated id of the order
 *         orderNumber:
 *           type: string
 *           description: Unique order number
 *         customerId:
 *           type: integer
 *           description: Customer ID
 *         items:
 *           type: array
 *           items:
 *             type: object
 *           description: Order items
 *         status:
 *           type: string
 *           enum: [pending, confirmed, processing, shipped, delivered, cancelled, refunded]
 *           description: Order status
 *         paymentStatus:
 *           type: string
 *           enum: [pending, paid, failed, refunded, partially_refunded]
 *           description: Payment status
 *         fulfillmentStatus:
 *           type: string
 *           enum: [unfulfilled, partial, fulfilled]
 *           description: Fulfillment status
 *         subtotal:
 *           type: number
 *           description: Subtotal amount
 *         taxAmount:
 *           type: number
 *           description: Tax amount
 *         shippingAmount:
 *           type: number
 *           description: Shipping amount
 *         discountAmount:
 *           type: number
 *           description: Discount amount
 *         totalAmount:
 *           type: number
 *           description: Total amount
 *         currency:
 *           type: string
 *           description: Currency code
 *         shippingAddress:
 *           type: object
 *           description: Shipping address
 *         billingAddress:
 *           type: object
 *           description: Billing address
 *         paymentMethod:
 *           type: object
 *           description: Payment method details
 *         shippingMethod:
 *           type: object
 *           description: Shipping method details
 *         notes:
 *           type: string
 *           description: Order notes
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

const Order = sequelize.define('Order', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  orderNumber: {
    type: DataTypes.STRING(50),
    unique: true,
    allowNull: false
  },
  customerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  storeId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'stores',
      key: 'id'
    }
  },
  items: {
    type: DataTypes.JSON,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Order must contain at least one item'
      }
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'),
    defaultValue: 'pending'
  },
  paymentStatus: {
    type: DataTypes.ENUM('pending', 'paid', 'failed', 'refunded', 'partially_refunded'),
    defaultValue: 'pending'
  },
  fulfillmentStatus: {
    type: DataTypes.ENUM('unfulfilled', 'partial', 'fulfilled'),
    defaultValue: 'unfulfilled'
  },
  subtotal: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  taxAmount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  shippingAmount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  discountAmount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  totalAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'USD'
  },
  shippingAddress: {
    type: DataTypes.JSON,
    allowNull: false
  },
  billingAddress: {
    type: DataTypes.JSON,
    allowNull: true
  },
  paymentMethod: {
    type: DataTypes.JSON,
    allowNull: false
  },
  shippingMethod: {
    type: DataTypes.JSON,
    allowNull: true
  },
  coupon: {
    type: DataTypes.JSON,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  customerNotes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  tags: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  source: {
    type: DataTypes.ENUM('web', 'mobile', 'api', 'admin'),
    defaultValue: 'web'
  },
  ipAddress: {
    type: DataTypes.STRING(45),
    allowNull: true
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  referrer: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  timeline: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  refunds: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  cancellationReason: {
    type: DataTypes.STRING(500),
    allowNull: true
  },
  cancelledAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  confirmedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  shippedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  deliveredAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  estimatedDeliveryDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  metaData: {
    type: DataTypes.JSON,
    defaultValue: {}
  }
}, {
  tableName: 'orders'
});

// Hooks
Order.beforeSave(async (order) => {
  // Generate order number if not provided
  if (!order.orderNumber && order.isNewRecord) {
    const count = await Order.count();
    order.orderNumber = `ORD-${Date.now()}-${(count + 1).toString().padStart(6, '0')}`;
  }

  // Calculate totals if items changed
  if (order.changed('items')) {
    const items = Array.isArray(order.items) ? order.items : [];
    order.subtotal = items.reduce((total, item) => total + (item.totalPrice || 0), 0);
    order.totalAmount = parseFloat(order.subtotal) + parseFloat(order.taxAmount || 0) + 
                      parseFloat(order.shippingAmount || 0) - parseFloat(order.discountAmount || 0);
  }

  // Add timeline entry if status changed
  if (order.changed('status') && !order.isNewRecord) {
    const timeline = Array.isArray(order.timeline) ? [...order.timeline] : [];
    timeline.push({
      status: order.status,
      timestamp: new Date(),
      note: null
    });
    order.timeline = timeline;
  }

  // Set status timestamps
  if (order.changed('status')) {
    const now = new Date();
    switch (order.status) {
      case 'confirmed':
        if (!order.confirmedAt) order.confirmedAt = now;
        break;
      case 'shipped':
        if (!order.shippedAt) order.shippedAt = now;
        break;
      case 'delivered':
        if (!order.deliveredAt) order.deliveredAt = now;
        break;
      case 'cancelled':
        if (!order.cancelledAt) order.cancelledAt = now;
        break;
    }
  }
});

// Virtual getters
Order.prototype.getTotalItems = function() {
  if (!Array.isArray(this.items)) return 0;
  return this.items.reduce((total, item) => total + (item.quantity || 0), 0);
};

Order.prototype.getMerchants = function() {
  if (!Array.isArray(this.items)) return [];
  const merchantIds = this.items.map(item => item.merchantId?.toString()).filter(Boolean);
  return [...new Set(merchantIds)];
};

module.exports = Order;
