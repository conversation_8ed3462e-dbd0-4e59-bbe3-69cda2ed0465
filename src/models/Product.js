const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     Product:
 *       type: object
 *       required:
 *         - name
 *         - description
 *         - price
 *         - categoryId
 *         - merchantId
 *       properties:
 *         id:
 *           type: integer
 *           description: The auto-generated id of the product
 *         name:
 *           type: string
 *           description: Product name
 *         description:
 *           type: string
 *           description: Product description
 *         shortDescription:
 *           type: string
 *           description: Short product description
 *         sku:
 *           type: string
 *           description: Stock Keeping Unit
 *         price:
 *           type: number
 *           description: Product price
 *         comparePrice:
 *           type: number
 *           description: Compare at price (original price)
 *         costPrice:
 *           type: number
 *           description: Cost price for merchant
 *         currency:
 *           type: string
 *           description: Currency code
 *         categoryId:
 *           type: integer
 *           description: Category ID
 *         brandId:
 *           type: integer
 *           description: Brand ID
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: Product tags
 *         images:
 *           type: array
 *           items:
 *             type: object
 *           description: Product images
 *         variants:
 *           type: array
 *           items:
 *             type: object
 *           description: Product variants
 *         inventory:
 *           type: object
 *           description: Inventory information
 *         shipping:
 *           type: object
 *           description: Shipping information
 *         seo:
 *           type: object
 *           description: SEO information
 *         status:
 *           type: string
 *           enum: [draft, active, archived]
 *           description: Product status
 *         featured:
 *           type: boolean
 *           description: Whether product is featured
 *         merchantId:
 *           type: integer
 *           description: Merchant ID
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

const Product = sequelize.define('Product', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(200),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Product name is required'
      },
      len: {
        args: [2, 200],
        msg: 'Product name must be between 2 and 200 characters'
      }
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Product description is required'
      }
    }
  },
  shortDescription: {
    type: DataTypes.STRING(500),
    allowNull: true,
    validate: {
      len: {
        args: [0, 500],
        msg: 'Short description cannot exceed 500 characters'
      }
    }
  },
  sku: {
    type: DataTypes.STRING(100),
    unique: true,
    allowNull: true
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  comparePrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  costPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'USD'
  },
  categoryId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'categories',
      key: 'id'
    }
  },
  brandId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  tags: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  images: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  variants: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  options: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  inventory: {
    type: DataTypes.JSON,
    defaultValue: {
      quantity: 0,
      trackQuantity: true,
      allowBackorder: false,
      lowStockThreshold: 5
    }
  },
  shipping: {
    type: DataTypes.JSON,
    defaultValue: {
      weight: null,
      dimensions: {
        length: null,
        width: null,
        height: null
      },
      requiresShipping: true,
      shippingClass: null
    }
  },
  seo: {
    type: DataTypes.JSON,
    defaultValue: {
      title: null,
      description: null,
      keywords: [],
      slug: null
    }
  },
  status: {
    type: DataTypes.ENUM('draft', 'active', 'archived'),
    defaultValue: 'draft'
  },
  featured: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  digital: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  downloadable: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  downloadFiles: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  merchantId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  storeId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'stores',
      key: 'id'
    }
  },
  ratings: {
    type: DataTypes.JSON,
    defaultValue: {
      average: 0,
      count: 0
    }
  },
  salesCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  viewsCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  publishedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  metaData: {
    type: DataTypes.JSON,
    defaultValue: {}
  }
}, {
  tableName: 'products'
});

// Hooks
Product.beforeSave(async (product) => {
  // Generate SKU if not provided
  if (!product.sku && product.isNewRecord) {
    product.sku = `PRD-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
  }

  // Generate slug if not provided
  if (product.changed('name') && (!product.seo || !product.seo.slug)) {
    const slug = product.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
    
    product.seo = { ...product.seo, slug };
  }

  // Set published date when status changes to active
  if (product.changed('status') && product.status === 'active' && !product.publishedAt) {
    product.publishedAt = new Date();
  }
});

// Virtual getters
Product.prototype.getDiscountPercentage = function() {
  if (this.comparePrice && this.comparePrice > this.price) {
    return Math.round(((this.comparePrice - this.price) / this.comparePrice) * 100);
  }
  return 0;
};

Product.prototype.getAvailable = function() {
  if (!this.inventory.trackQuantity) return true;
  return this.inventory.quantity > 0 || this.inventory.allowBackorder;
};

Product.prototype.getMainImage = function() {
  if (!this.images || this.images.length === 0) return null;
  const mainImg = this.images.find(img => img.isMain);
  return mainImg ? mainImg.url : this.images[0].url;
};

module.exports = Product;
