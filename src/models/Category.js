const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     Category:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         id:
 *           type: integer
 *           description: The auto-generated id of the category
 *         name:
 *           type: string
 *           description: Category name
 *         description:
 *           type: string
 *           description: Category description
 *         slug:
 *           type: string
 *           description: URL-friendly category identifier
 *         image:
 *           type: string
 *           description: Category image URL
 *         icon:
 *           type: string
 *           description: Category icon
 *         parentId:
 *           type: integer
 *           description: Parent category ID
 *         level:
 *           type: number
 *           description: Category hierarchy level
 *         position:
 *           type: number
 *           description: Display position
 *         isActive:
 *           type: boolean
 *           description: Whether category is active
 *         isFeatured:
 *           type: boolean
 *           description: Whether category is featured
 *         seo:
 *           type: object
 *           description: SEO information
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

const Category = sequelize.define('Category', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Category name is required'
      },
      len: {
        args: [2, 100],
        msg: 'Category name must be between 2 and 100 characters'
      }
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    validate: {
      len: {
        args: [0, 500],
        msg: 'Description cannot exceed 500 characters'
      }
    }
  },
  slug: {
    type: DataTypes.STRING(150),
    unique: true,
    allowNull: true
  },
  image: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: null
  },
  icon: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  parentId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  storeId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'stores',
      key: 'id'
    }
  },
  level: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  position: {
    type: DataTypes.INTEGER,
    defaultValue: 0
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  isFeatured: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  seo: {
    type: DataTypes.JSON,
    defaultValue: {
      title: null,
      description: null,
      keywords: []
    }
  },
  attributes: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  metaData: {
    type: DataTypes.JSON,
    defaultValue: {}
  }
}, {
  tableName: 'categories'
});

// Self-referencing association for parent-child relationship
Category.belongsTo(Category, { 
  as: 'parent', 
  foreignKey: 'parentId',
  onDelete: 'SET NULL'
});

Category.hasMany(Category, { 
  as: 'children', 
  foreignKey: 'parentId',
  onDelete: 'CASCADE'
});

// Hooks
Category.beforeSave(async (category) => {
  // Generate slug if not provided
  if (category.changed('name') && !category.slug) {
    category.slug = category.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }

  // Set level based on parent
  if (category.changed('parentId')) {
    if (category.parentId) {
      const parent = await Category.findByPk(category.parentId);
      if (parent) {
        category.level = parent.level + 1;
      }
    } else {
      category.level = 0;
    }
  }
});

// Instance methods
Category.prototype.getPath = async function() {
  const path = [];
  let current = this;
  
  while (current) {
    path.unshift({
      id: current.id,
      name: current.name,
      slug: current.slug
    });
    
    if (current.parentId) {
      current = await Category.findByPk(current.parentId);
    } else {
      current = null;
    }
  }
  
  return path;
};

Category.prototype.getDescendants = async function() {
  const descendants = [];
  
  const findChildren = async (parentId) => {
    const children = await Category.findAll({ where: { parentId } });
    for (const child of children) {
      descendants.push(child);
      await findChildren(child.id);
    }
  };
  
  await findChildren(this.id);
  return descendants;
};

// Static methods
Category.buildTree = async function(parentId = null) {
  const categories = await this.findAll({ 
    where: { 
      parentId, 
      isActive: true 
    },
    order: [['position', 'ASC'], ['name', 'ASC']]
  });
  
  const tree = [];
  
  for (const category of categories) {
    const categoryObj = category.toJSON();
    categoryObj.children = await this.buildTree(category.id);
    tree.push(categoryObj);
  }
  
  return tree;
};

Category.getFeatured = function() {
  return this.findAll({ 
    where: { 
      isFeatured: true, 
      isActive: true 
    },
    order: [['position', 'ASC'], ['name', 'ASC']]
  });
};

module.exports = Category;
