const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     Coupon:
 *       type: object
 *       required:
 *         - code
 *         - type
 *         - value
 *         - description
 *       properties:
 *         id:
 *           type: integer
 *           description: The auto-generated id of the coupon
 *         code:
 *           type: string
 *           description: Unique coupon code
 *         type:
 *           type: string
 *           enum: [percentage, fixed, free_shipping]
 *           description: Coupon type
 *         value:
 *           type: number
 *           description: Discount value
 *         description:
 *           type: string
 *           description: Coupon description
 *         minimumAmount:
 *           type: number
 *           description: Minimum order amount required
 *         maximumDiscount:
 *           type: number
 *           description: Maximum discount amount
 *         usageLimit:
 *           type: number
 *           description: Total usage limit
 *         usedCount:
 *           type: number
 *           description: Number of times used
 *         userLimit:
 *           type: number
 *           description: Usage limit per user
 *         startDate:
 *           type: string
 *           format: date
 *           description: Coupon start date
 *         endDate:
 *           type: string
 *           format: date
 *           description: Coupon end date
 *         active:
 *           type: boolean
 *           description: Whether coupon is active
 *         firstTimeCustomersOnly:
 *           type: boolean
 *           description: Only for first-time customers
 *         applicableProducts:
 *           type: array
 *           items:
 *             type: integer
 *           description: Applicable product IDs
 *         applicableCategories:
 *           type: array
 *           items:
 *             type: integer
 *           description: Applicable category IDs
 *         excludedProducts:
 *           type: array
 *           items:
 *             type: integer
 *           description: Excluded product IDs
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

const Coupon = sequelize.define('Coupon', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: {
        msg: 'Coupon code is required'
      },
      len: {
        args: [3, 50],
        msg: 'Coupon code must be between 3 and 50 characters'
      },
      isUppercase: {
        msg: 'Coupon code must be uppercase'
      }
    }
  },
  type: {
    type: DataTypes.ENUM('percentage', 'fixed', 'free_shipping'),
    allowNull: false
  },
  value: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0
    }
  },
  description: {
    type: DataTypes.STRING(500),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Description is required'
      },
      len: {
        args: [5, 500],
        msg: 'Description must be between 5 and 500 characters'
      }
    }
  },
  minimumAmount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  maximumDiscount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    }
  },
  usageLimit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    }
  },
  usedCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  userLimit: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    }
  },
  startDate: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  endDate: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    validate: {
      isAfterStartDate(value) {
        if (value <= this.startDate) {
          throw new Error('End date must be after start date');
        }
      }
    }
  },
  active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  firstTimeCustomersOnly: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  applicableProducts: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  applicableCategories: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  excludedProducts: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  metaData: {
    type: DataTypes.JSON,
    defaultValue: {}
  },
  storeId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'stores',
      key: 'id'
    }
  }
}, {
  tableName: 'coupons'
});

// Hooks
Coupon.beforeSave(async (coupon) => {
  // Ensure code is uppercase
  if (coupon.changed('code')) {
    coupon.code = coupon.code.toUpperCase();
  }

  // Validate percentage value
  if (coupon.type === 'percentage' && coupon.value > 100) {
    throw new Error('Percentage value cannot exceed 100');
  }
});

// Instance methods
Coupon.prototype.isValid = function() {
  const now = new Date();
  const startDate = new Date(this.startDate);
  const endDate = new Date(this.endDate);
  
  return this.active && 
         now >= startDate && 
         now <= endDate &&
         (!this.usageLimit || this.usedCount < this.usageLimit);
};

Coupon.prototype.canBeUsedBy = function(customerId, orderTotal) {
  if (!this.isValid()) return false;
  
  // Check minimum amount
  if (this.minimumAmount && orderTotal < this.minimumAmount) {
    return false;
  }
  
  // TODO: Check user-specific limits and first-time customer restrictions
  // This would require additional database queries
  
  return true;
};

Coupon.prototype.calculateDiscount = function(orderTotal) {
  if (!this.isValid()) return 0;
  
  let discount = 0;
  
  switch (this.type) {
    case 'percentage':
      discount = (orderTotal * this.value) / 100;
      if (this.maximumDiscount) {
        discount = Math.min(discount, this.maximumDiscount);
      }
      break;
    case 'fixed':
      discount = Math.min(this.value, orderTotal);
      break;
    case 'free_shipping':
      discount = 0; // Shipping discount handled separately
      break;
  }
  
  return Math.round(discount * 100) / 100;
};

// Static methods
Coupon.findByCode = function(code) {
  return this.findOne({ where: { code: code.toUpperCase() } });
};

Coupon.getActive = function() {
  const { Op } = require('sequelize');
  const now = new Date();
  
  return this.findAll({
    where: {
      active: true,
      startDate: { [Op.lte]: now },
      endDate: { [Op.gte]: now }
    },
    order: [['createdAt', 'DESC']]
  });
};

module.exports = Coupon;
