const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     Store:
 *       type: object
 *       required:
 *         - name
 *         - ownerId
 *         - domain
 *       properties:
 *         id:
 *           type: integer
 *           description: The auto-generated id of the store
 *         name:
 *           type: string
 *           description: Store name
 *         description:
 *           type: string
 *           description: Store description
 *         ownerId:
 *           type: integer
 *           description: Store owner (admin) user ID
 *         domain:
 *           type: string
 *           description: Store domain/subdomain
 *         logo:
 *           type: string
 *           description: Store logo URL
 *         banner:
 *           type: string
 *           description: Store banner URL
 *         theme:
 *           type: object
 *           description: Store theme settings
 *         settings:
 *           type: object
 *           description: Store configuration settings
 *         socialMedia:
 *           type: object
 *           description: Social media links
 *         contactInfo:
 *           type: object
 *           description: Contact information
 *         businessInfo:
 *           type: object
 *           description: Business registration info
 *         isActive:
 *           type: boolean
 *           description: Whether store is active
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

const Store = sequelize.define('Store', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    validate: {
      notEmpty: {
        msg: 'Store name is required'
      },
      len: {
        args: [2, 100],
        msg: 'Store name must be between 2 and 100 characters'
      }
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  ownerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  domain: {
    type: DataTypes.STRING(100),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: {
        msg: 'Domain is required'
      },
      isLowercase: {
        msg: 'Domain must be lowercase'
      },
      is: {
        args: /^[a-z0-9-]+$/,
        msg: 'Domain can only contain lowercase letters, numbers, and hyphens'
      }
    }
  },
  logo: {
    type: DataTypes.STRING(500),
    allowNull: true,
    validate: {
      isUrl: {
        msg: 'Logo must be a valid URL'
      }
    }
  },
  banner: {
    type: DataTypes.STRING(500),
    allowNull: true,
    validate: {
      isUrl: {
        msg: 'Banner must be a valid URL'
      }
    }
  },
  theme: {
    type: DataTypes.JSON,
    defaultValue: {
      primaryColor: '#ec4899',
      secondaryColor: '#9c27b0',
      fontFamily: 'Inter',
      layout: 'modern'
    }
  },
  settings: {
    type: DataTypes.JSON,
    defaultValue: {
      currency: 'USD',
      language: 'en',
      timezone: 'UTC',
      taxRate: 0,
      shippingEnabled: true,
      guestCheckout: true,
      emailNotifications: true,
      smsNotifications: false,
      inventoryTracking: true,
      lowStockAlert: 5,
      autoApproveReviews: false,
      maintenanceMode: false
    }
  },
  socialMedia: {
    type: DataTypes.JSON,
    defaultValue: {
      facebook: '',
      twitter: '',
      instagram: '',
      linkedin: '',
      youtube: '',
      tiktok: ''
    }
  },
  contactInfo: {
    type: DataTypes.JSON,
    defaultValue: {
      email: '',
      phone: '',
      address: '',
      city: '',
      state: '',
      country: '',
      zipCode: '',
      workingHours: {
        monday: '9:00 AM - 6:00 PM',
        tuesday: '9:00 AM - 6:00 PM',
        wednesday: '9:00 AM - 6:00 PM',
        thursday: '9:00 AM - 6:00 PM',
        friday: '9:00 AM - 6:00 PM',
        saturday: '10:00 AM - 4:00 PM',
        sunday: 'Closed'
      }
    }
  },
  businessInfo: {
    type: DataTypes.JSON,
    defaultValue: {
      businessName: '',
      registrationNumber: '',
      taxNumber: '',
      businessType: '',
      establishedYear: null,
      website: ''
    }
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  subscription: {
    type: DataTypes.JSON,
    defaultValue: {
      plan: 'basic',
      status: 'active',
      expiresAt: null,
      features: {
        maxProducts: 100,
        maxOrders: 1000,
        customDomain: false,
        advancedAnalytics: false,
        prioritySupport: false
      }
    }
  },
  analytics: {
    type: DataTypes.JSON,
    defaultValue: {
      totalViews: 0,
      totalSales: 0,
      totalOrders: 0,
      totalCustomers: 0,
      conversionRate: 0,
      averageOrderValue: 0
    }
  }
}, {
  tableName: 'stores'
});

// Hooks
Store.beforeSave(async (store) => {
  // Ensure domain is lowercase and valid
  if (store.changed('domain')) {
    store.domain = store.domain.toLowerCase().trim();
  }
});

// Instance methods
Store.prototype.updateAnalytics = function(data) {
  this.analytics = {
    ...this.analytics,
    ...data,
    updatedAt: new Date()
  };
  return this.save();
};

Store.prototype.isOwner = function(userId) {
  return this.ownerId === userId;
};

Store.prototype.canAccess = function(userId) {
  return this.ownerId === userId || this.isActive;
};

Store.prototype.getPublicData = function() {
  return {
    id: this.id,
    name: this.name,
    description: this.description,
    domain: this.domain,
    logo: this.logo,
    banner: this.banner,
    theme: this.theme,
    socialMedia: this.socialMedia,
    contactInfo: this.contactInfo,
    businessInfo: this.businessInfo,
    isActive: this.isActive
  };
};

// Static methods
Store.findByDomain = function(domain) {
  return this.findOne({ 
    where: { 
      domain: domain.toLowerCase(),
      isActive: true 
    } 
  });
};

Store.findByOwner = function(ownerId) {
  return this.findOne({ 
    where: { ownerId } 
  });
};

module.exports = Store;
