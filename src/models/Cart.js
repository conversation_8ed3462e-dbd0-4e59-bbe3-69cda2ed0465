const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     Cart:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The auto-generated id of the cart
 *         userId:
 *           type: integer
 *           description: User ID (optional for guest carts)
 *         sessionId:
 *           type: string
 *           description: Session ID for guest carts
 *         items:
 *           type: array
 *           items:
 *             type: object
 *           description: Cart items
 *         subtotal:
 *           type: number
 *           description: Subtotal amount
 *         totalItems:
 *           type: number
 *           description: Total number of items
 *         currency:
 *           type: string
 *           description: Currency code
 *         appliedCoupons:
 *           type: array
 *           items:
 *             type: object
 *           description: Applied coupons
 *         shippingAddress:
 *           type: object
 *           description: Shipping address
 *         notes:
 *           type: string
 *           description: Cart notes
 *         expiresAt:
 *           type: string
 *           format: date-time
 *           description: Cart expiration date
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 */

const Cart = sequelize.define('Cart', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  sessionId: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  items: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  subtotal: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  totalItems: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  currency: {
    type: DataTypes.STRING(3),
    defaultValue: 'USD'
  },
  appliedCoupons: {
    type: DataTypes.JSON,
    defaultValue: []
  },
  discountAmount: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    validate: {
      min: 0
    }
  },
  shippingAddress: {
    type: DataTypes.JSON,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('active', 'abandoned', 'converted', 'expired'),
    defaultValue: 'active'
  },
  lastActivity: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  expiresAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  source: {
    type: DataTypes.ENUM('web', 'mobile', 'api'),
    defaultValue: 'web'
  },
  ipAddress: {
    type: DataTypes.STRING(45),
    allowNull: true
  },
  userAgent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  metaData: {
    type: DataTypes.JSON,
    defaultValue: {}
  }
}, {
  tableName: 'carts'
});

// Hooks
Cart.beforeSave(async (cart) => {
  // Set expiration date for new carts
  if (cart.isNewRecord && !cart.expiresAt) {
    const days = cart.userId ? 30 : 7; // 30 days for users, 7 for guests
    cart.expiresAt = new Date(Date.now() + days * 24 * 60 * 60 * 1000);
  }

  // Calculate totals if items or coupons changed
  if (cart.changed('items') || cart.changed('appliedCoupons')) {
    const items = Array.isArray(cart.items) ? cart.items : [];
    
    // Calculate subtotal and total items
    cart.subtotal = items.reduce((total, item) => total + (item.totalPrice || 0), 0);
    cart.totalItems = items.reduce((total, item) => total + (item.quantity || 0), 0);
    
    // Calculate total discount
    const coupons = Array.isArray(cart.appliedCoupons) ? cart.appliedCoupons : [];
    cart.discountAmount = coupons.reduce((total, coupon) => total + (coupon.discountAmount || 0), 0);
    
    // Update last activity
    cart.lastActivity = new Date();
  }

  // Update item totals
  if (cart.changed('items')) {
    const items = Array.isArray(cart.items) ? cart.items : [];
    cart.items = items.map(item => ({
      ...item,
      totalPrice: (item.price || 0) * (item.quantity || 0)
    }));
  }
});

// Instance methods
Cart.prototype.addItem = async function(productId, variantId, quantity, price, options = []) {
  const items = Array.isArray(this.items) ? [...this.items] : [];
  
  const existingItemIndex = items.findIndex(item => 
    item.productId?.toString() === productId.toString() && 
    (!variantId || item.variantId?.toString() === variantId.toString()) &&
    JSON.stringify(item.options || []) === JSON.stringify(options)
  );

  if (existingItemIndex > -1) {
    // Update existing item
    items[existingItemIndex].quantity += quantity;
    items[existingItemIndex].totalPrice = items[existingItemIndex].price * items[existingItemIndex].quantity;
  } else {
    // Add new item
    items.push({
      id: Date.now() + Math.random(), // Temporary ID
      productId,
      variantId,
      quantity,
      price,
      totalPrice: price * quantity,
      options,
      addedAt: new Date()
    });
  }

  this.items = items;
  return await this.save();
};

Cart.prototype.updateItemQuantity = async function(itemId, quantity) {
  const items = Array.isArray(this.items) ? [...this.items] : [];
  const itemIndex = items.findIndex(item => item.id?.toString() === itemId.toString());
  
  if (itemIndex === -1) {
    throw new Error('Item not found in cart');
  }

  if (quantity <= 0) {
    items.splice(itemIndex, 1);
  } else {
    items[itemIndex].quantity = quantity;
    items[itemIndex].totalPrice = items[itemIndex].price * quantity;
  }

  this.items = items;
  return await this.save();
};

Cart.prototype.removeItem = async function(itemId) {
  const items = Array.isArray(this.items) ? [...this.items] : [];
  this.items = items.filter(item => item.id?.toString() !== itemId.toString());
  return await this.save();
};

Cart.prototype.clear = async function() {
  this.items = [];
  this.appliedCoupons = [];
  this.discountAmount = 0;
  this.notes = '';
  return await this.save();
};

Cart.prototype.applyCoupon = async function(couponCode, couponType, couponValue, discountAmount) {
  const coupons = Array.isArray(this.appliedCoupons) ? [...this.appliedCoupons] : [];
  
  // Remove existing coupon with same code
  const filteredCoupons = coupons.filter(c => c.code !== couponCode);
  
  // Add new coupon
  filteredCoupons.push({
    code: couponCode,
    type: couponType,
    value: couponValue,
    discountAmount,
    appliedAt: new Date()
  });

  this.appliedCoupons = filteredCoupons;
  return await this.save();
};

Cart.prototype.removeCoupon = async function(couponCode) {
  const coupons = Array.isArray(this.appliedCoupons) ? [...this.appliedCoupons] : [];
  this.appliedCoupons = coupons.filter(c => c.code !== couponCode);
  return await this.save();
};

// Virtual getters
Cart.prototype.getTotalDiscount = function() {
  const coupons = Array.isArray(this.appliedCoupons) ? this.appliedCoupons : [];
  return coupons.reduce((total, coupon) => total + (coupon.discountAmount || 0), 0);
};

Cart.prototype.getTotal = function() {
  return Math.max(0, parseFloat(this.subtotal || 0) - parseFloat(this.discountAmount || 0));
};

Cart.prototype.getMerchants = function() {
  const items = Array.isArray(this.items) ? this.items : [];
  const merchantIds = items.map(item => item.merchantId?.toString()).filter(Boolean);
  return [...new Set(merchantIds)];
};

// Static methods
Cart.findByUserOrSession = function(userId, sessionId) {
  const where = { status: 'active' };
  
  if (userId) {
    where.userId = userId;
  } else if (sessionId) {
    where.sessionId = sessionId;
  } else {
    return null;
  }
  
  return this.findOne({ where });
};

Cart.mergeCarts = async function(guestCart, userCart) {
  if (!guestCart) return userCart;
  if (!userCart) return guestCart;

  // Merge items from guest cart to user cart
  const guestItems = Array.isArray(guestCart.items) ? guestCart.items : [];
  
  for (const guestItem of guestItems) {
    await userCart.addItem(
      guestItem.productId,
      guestItem.variantId,
      guestItem.quantity,
      guestItem.price,
      guestItem.options
    );
  }

  // Remove guest cart
  await guestCart.destroy();
  
  return userCart;
};

module.exports = Cart;
