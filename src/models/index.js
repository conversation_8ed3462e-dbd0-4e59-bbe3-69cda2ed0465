const { sequelize } = require('../config/database');

// Import all models
const User = require('./User');
const Store = require('./Store');
const Category = require('./Category');
const Product = require('./Product');
const Order = require('./Order');
const Cart = require('./Cart');
const Coupon = require('./Coupon');

// Define associations
const setupAssociations = () => {
  // User associations
  User.hasOne(Store, {
    foreignKey: 'ownerId',
    as: 'store',
    onDelete: 'CASCADE'
  });

  User.hasMany(Product, {
    foreignKey: 'merchantId',
    as: 'products',
    onDelete: 'CASCADE'
  });
  
  User.hasMany(Order, { 
    foreignKey: 'customerId', 
    as: 'orders',
    onDelete: 'CASCADE'
  });
  
  User.hasMany(Cart, {
    foreignKey: 'userId',
    as: 'carts',
    onDelete: 'CASCADE'
  });

  // Store associations
  Store.belongsTo(User, {
    foreignKey: 'ownerId',
    as: 'owner'
  });

  Store.hasMany(Category, {
    foreignKey: 'storeId',
    as: 'categories',
    onDelete: 'CASCADE'
  });

  Store.hasMany(Product, {
    foreignKey: 'storeId',
    as: 'products',
    onDelete: 'CASCADE'
  });

  Store.hasMany(Order, {
    foreignKey: 'storeId',
    as: 'orders',
    onDelete: 'CASCADE'
  });

  Store.hasMany(Coupon, {
    foreignKey: 'storeId',
    as: 'coupons',
    onDelete: 'CASCADE'
  });

  // Category associations
  Category.hasMany(Product, { 
    foreignKey: 'categoryId', 
    as: 'products',
    onDelete: 'RESTRICT'
  });

  // Product associations
  Product.belongsTo(User, { 
    foreignKey: 'merchantId', 
    as: 'merchant'
  });
  
  Product.belongsTo(Category, { 
    foreignKey: 'categoryId', 
    as: 'category'
  });

  // Order associations
  Order.belongsTo(User, { 
    foreignKey: 'customerId', 
    as: 'customer'
  });

  // Cart associations
  Cart.belongsTo(User, { 
    foreignKey: 'userId', 
    as: 'user'
  });
};

// Setup associations
setupAssociations();

// Export models and sequelize instance
module.exports = {
  sequelize,
  User,
  Store,
  Category,
  Product,
  Order,
  Cart,
  Coupon
};
