const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');
require('dotenv').config();

// Database configuration
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  database: process.env.DB_NAME || 'tok_ecommerce',
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  dialect: process.env.DB_DIALECT || 'mysql',
  logging: process.env.NODE_ENV === 'development' ? (msg) => logger.info(msg) : false,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true
  }
};

// Create Sequelize instance
const sequelize = new Sequelize(config.database, config.username, config.password, {
  host: config.host,
  port: config.port,
  dialect: config.dialect,
  logging: config.logging,
  pool: config.pool,
  define: config.define
});

// Create database if it doesn't exist
const createDatabase = async () => {
  const mysql = require('mysql2/promise');

  try {
    const connection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      user: config.username,
      password: config.password
    });

    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${config.database}\``);
    await connection.end();

    logger.info(`✅ Database '${config.database}' created or already exists`);
  } catch (error) {
    logger.error('❌ Failed to create database:', error);
    throw error;
  }
};

// Test database connection
const connectDB = async () => {
  try {
    // First, create database if it doesn't exist
    await createDatabase();

    // Then connect to the database
    await sequelize.authenticate();
    logger.info(`✅ MySQL Connected: ${config.host}:${config.port}/${config.database}`);

    // Sync database in development
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ force: false });
      logger.info('🔄 Database synchronized');
    }

    return sequelize;
  } catch (error) {
    logger.error('❌ Database connection failed:', error);
    process.exit(1);
  }
};

module.exports = {
  sequelize,
  connectDB,
  Sequelize
};
