<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>Test Store Registration</h1>
        <form id="registrationForm">
            <div class="form-group">
                <label for="name">Full Name:</label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">Confirm Password:</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required>
            </div>
            
            <div class="form-group">
                <label for="phone">Phone:</label>
                <input type="tel" id="phone" name="phone">
            </div>
            
            <div class="form-group">
                <label for="storeName">Store Name:</label>
                <input type="text" id="storeName" name="storeName" required>
            </div>
            
            <div class="form-group">
                <label for="storeDomain">Store Domain:</label>
                <input type="text" id="storeDomain" name="storeDomain" required>
                <small>This will be your store's web address (e.g., my-store.salla.com)</small>
            </div>
            
            <div class="form-group">
                <label for="storeDescription">Store Description:</label>
                <textarea id="storeDescription" name="storeDescription" rows="3"></textarea>
            </div>
            
            <button type="submit" id="submitBtn">Create Store</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('registrationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            
            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.textContent = 'Creating Store...';
            resultDiv.innerHTML = '';
            
            // Get form data
            const formData = new FormData(this);
            const data = {
                name: formData.get('name'),
                email: formData.get('email'),
                password: formData.get('password'),
                passwordConfirm: formData.get('confirmPassword'),
                phone: formData.get('phone'),
                role: 'admin',
                storeName: formData.get('storeName'),
                storeDomain: formData.get('storeDomain'),
                storeDescription: formData.get('storeDescription')
            };
            
            try {
                const response = await fetch('http://localhost:3000/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Store Created Successfully!</h3>
                            <p><strong>Store Name:</strong> ${data.storeName}</p>
                            <p><strong>Store Domain:</strong> ${data.storeDomain}.salla.com</p>
                            <p><strong>Owner Email:</strong> ${data.email}</p>
                            <p>You can now login to your admin dashboard!</p>
                        </div>
                    `;
                    this.reset();
                } else {
                    throw new Error(result.message || 'Registration failed');
                }
                
            } catch (error) {
                console.error('Registration error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Registration Failed</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Create Store';
            }
        });
        
        // Auto-generate domain from store name
        document.getElementById('storeName').addEventListener('input', function() {
            const storeName = this.value;
            const domainField = document.getElementById('storeDomain');
            
            if (storeName && !domainField.value) {
                const domain = storeName
                    .toLowerCase()
                    .replace(/[^a-z0-9\s]/g, '')
                    .replace(/\s+/g, '-')
                    .substring(0, 30);
                domainField.value = domain;
            }
        });
        
        // Generate unique email for testing
        document.getElementById('email').value = `test${Date.now()}@example.com`;
        document.getElementById('storeDomain').value = `test-store-${Date.now()}`;
    </script>
</body>
</html>
