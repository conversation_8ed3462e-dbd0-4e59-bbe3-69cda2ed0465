// Test Analytics API
const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

async function testAnalytics() {
  try {
    console.log('🧪 Testing Analytics API...\n');
    
    // 1. Login as admin
    console.log('1. Logging in as admin...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'AdminPass123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Admin login successful');
    
    // 2. Test Dashboard Stats
    console.log('\n2. Testing dashboard stats...');
    const dashboardResponse = await axios.get(`${API_BASE}/analytics/dashboard`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Dashboard stats retrieved:');
    console.log(JSON.stringify(dashboardResponse.data, null, 2));
    
    // 3. Test Sales Data
    console.log('\n3. Testing sales data...');
    const salesResponse = await axios.get(`${API_BASE}/analytics/sales?period=7d`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Sales data retrieved:');
    console.log(JSON.stringify(salesResponse.data, null, 2));
    
    // 4. Test Top Products
    console.log('\n4. Testing top products...');
    const productsResponse = await axios.get(`${API_BASE}/analytics/top-products?limit=5`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Top products retrieved:');
    console.log(JSON.stringify(productsResponse.data, null, 2));
    
    // 5. Test Recent Orders
    console.log('\n5. Testing recent orders...');
    const ordersResponse = await axios.get(`${API_BASE}/analytics/recent-orders?limit=5`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Recent orders retrieved:');
    console.log(JSON.stringify(ordersResponse.data, null, 2));
    
    // 6. Test Customer Stats
    console.log('\n6. Testing customer stats...');
    const customerResponse = await axios.get(`${API_BASE}/analytics/customer-stats`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    
    console.log('✅ Customer stats retrieved:');
    console.log(JSON.stringify(customerResponse.data, null, 2));
    
    console.log('\n🎉 All Analytics API tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testAnalytics();
