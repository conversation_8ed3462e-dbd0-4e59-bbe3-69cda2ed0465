// Complete API test script
const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

// Test all major API endpoints
async function runCompleteTests() {
  console.log('🚀 Starting Complete API Tests...\n');
  
  try {
    // 1. Health Check
    console.log('🧪 Testing health check...');
    const healthResponse = await axios.get('http://localhost:3000/health');
    console.log('✅ Health check successful:', healthResponse.data.status);
    
    // 2. Admin Login
    console.log('\n🧪 Testing admin login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'AdminPass123'
    });
    const adminToken = loginResponse.data.token;
    console.log('✅ Admin login successful');
    
    // 3. Get Categories
    console.log('\n🧪 Testing get categories...');
    const categoriesResponse = await axios.get(`${API_BASE}/categories`);
    console.log('✅ Categories retrieved:', categoriesResponse.data.data.categories.length, 'categories');
    
    // 4. Get Products
    console.log('\n🧪 Testing get products...');
    const productsResponse = await axios.get(`${API_BASE}/products`);
    console.log('✅ Products retrieved:', productsResponse.data.data.products.length, 'products');
    
    if (productsResponse.data.data.products.length > 0) {
      const product = productsResponse.data.data.products[0];
      console.log('   First product:', product.name, '- $' + product.price);
      
      // 5. Get Single Product
      console.log('\n🧪 Testing get single product...');
      const singleProductResponse = await axios.get(`${API_BASE}/products/${product.id}`);
      console.log('✅ Single product retrieved:', singleProductResponse.data.data.product.name);
    }
    
    // 6. Customer Registration
    console.log('\n🧪 Testing customer registration...');
    try {
      const customerResponse = await axios.post(`${API_BASE}/auth/register`, {
        name: 'Test Customer',
        email: '<EMAIL>',
        password: 'Customer123',
        passwordConfirm: 'Customer123',
        role: 'customer'
      });
      console.log('✅ Customer registration successful');
    } catch (error) {
      if (error.response?.status === 409) {
        console.log('✅ Customer already exists, trying login...');
      } else {
        console.log('❌ Customer registration failed:', error.response?.data?.message);
      }
    }
    
    // 7. Customer Login
    console.log('\n🧪 Testing customer login...');
    const customerLoginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'Customer123'
    });
    const customerToken = customerLoginResponse.data.token;
    console.log('✅ Customer login successful');
    
    // 8. Get Customer Profile
    console.log('\n🧪 Testing get customer profile...');
    const profileResponse = await axios.get(`${API_BASE}/auth/me`, {
      headers: { 'Authorization': `Bearer ${customerToken}` }
    });
    console.log('✅ Customer profile retrieved:', profileResponse.data.data.user.name);
    
    // 9. Get Cart
    console.log('\n🧪 Testing get cart...');
    const cartResponse = await axios.get(`${API_BASE}/cart`, {
      headers: { 'Authorization': `Bearer ${customerToken}` }
    });
    console.log('✅ Cart retrieved, items:', cartResponse.data.data.cart?.totalItems || 0);
    
    // 10. Add to Cart (if products exist)
    if (productsResponse.data.data.products.length > 0) {
      console.log('\n🧪 Testing add to cart...');
      const product = productsResponse.data.data.products[0];
      try {
        const addToCartResponse = await axios.post(`${API_BASE}/cart/items`, {
          productId: product.id,
          quantity: 2,
          price: parseFloat(product.price)
        }, {
          headers: { 'Authorization': `Bearer ${customerToken}` }
        });
        console.log('✅ Product added to cart successfully');
        
        // 11. Get Updated Cart
        console.log('\n🧪 Testing get updated cart...');
        const updatedCartResponse = await axios.get(`${API_BASE}/cart`, {
          headers: { 'Authorization': `Bearer ${customerToken}` }
        });
        console.log('✅ Updated cart retrieved, items:', updatedCartResponse.data.data.cart?.totalItems || 0);
      } catch (error) {
        console.log('❌ Add to cart failed:', error.response?.data?.message);
      }
    }
    
    // 12. Test Store Settings
    console.log('\n🧪 Testing store settings...');
    const storeResponse = await axios.get(`${API_BASE}/store/settings`);
    console.log('✅ Store settings retrieved');
    
    // 13. Test Store Info
    console.log('\n🧪 Testing store info...');
    const storeInfoResponse = await axios.get(`${API_BASE}/store/info`);
    console.log('✅ Store info retrieved:', storeInfoResponse.data.data.store.name);
    
    // 14. Test Payment Methods
    console.log('\n🧪 Testing payment methods...');
    const paymentMethodsResponse = await axios.get(`${API_BASE}/payments/methods`);
    console.log('✅ Payment methods retrieved:', paymentMethodsResponse.data.data.paymentMethods.length, 'methods');
    
    // 15. Test Shipping Methods
    console.log('\n🧪 Testing shipping methods...');
    const shippingMethodsResponse = await axios.get(`${API_BASE}/shipping/methods`);
    console.log('✅ Shipping methods retrieved:', shippingMethodsResponse.data.data.shippingMethods.length, 'methods');
    
    // 16. Test Coupons (Admin only)
    console.log('\n🧪 Testing get coupons (admin)...');
    const couponsResponse = await axios.get(`${API_BASE}/coupons`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log('✅ Coupons retrieved:', couponsResponse.data.data.coupons.length, 'coupons');
    
    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('✅ Health Check');
    console.log('✅ Authentication (Admin & Customer)');
    console.log('✅ User Profiles');
    console.log('✅ Categories');
    console.log('✅ Products');
    console.log('✅ Shopping Cart');
    console.log('✅ Store Settings');
    console.log('✅ Payment Methods');
    console.log('✅ Shipping Methods');
    console.log('✅ Coupons');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the complete tests
runCompleteTests();
