<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Redirect After Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #ccc; cursor: not-allowed; }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Test Login Redirect</h1>
        
        <div class="status info">
            <h3>📋 Test Instructions:</h3>
            <ol>
                <li>Click "Test Login & Redirect" below</li>
                <li>Watch the log for detailed information</li>
                <li>The test will simulate the login process</li>
                <li>Check if redirect methods work properly</li>
            </ol>
        </div>

        <button onclick="testLoginRedirect()" id="testBtn">Test Login & Redirect</button>
        <button onclick="clearLog()">Clear Log</button>
        <button onclick="testDirectRedirect()">Test Direct Redirect</button>

        <h3>📊 Test Log:</h3>
        <div id="log"></div>

        <div class="status warning">
            <h3>⚠️ Important Notes:</h3>
            <ul>
                <li>This test simulates the login redirect process</li>
                <li>Open the actual app at <strong>http://localhost:3000</strong> to test real login</li>
                <li>Use credentials: <strong><EMAIL></strong> / <strong>AdminPass123</strong></li>
                <li>Check browser console for additional logs</li>
            </ul>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        async function testLoginRedirect() {
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            btn.textContent = 'Testing...';
            
            try {
                log('🔄 Starting login redirect test...');
                
                // Simulate login API call
                log('📡 Simulating login API call...');
                const loginData = {
                    email: '<EMAIL>',
                    password: 'AdminPass123'
                };
                
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(loginData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    log('✅ Login API successful');
                    log(`👤 User: ${result.data.user.name}`);
                    log(`🔑 Token received: ${result.token ? 'Yes' : 'No'}`);
                    
                    // Test redirect methods
                    log('🔄 Testing redirect methods...');
                    
                    // Method 1: History API
                    log('📍 Method 1: Testing History API...');
                    if (window.history && window.history.pushState) {
                        log('✅ History API available');
                        // Don't actually redirect in test
                        log('🔄 Would execute: history.pushState({}, "", "/dashboard")');
                    } else {
                        log('❌ History API not available');
                    }
                    
                    // Method 2: Location replace
                    log('📍 Method 2: Testing Location replace...');
                    log('🔄 Would execute: window.location.replace("/dashboard")');
                    
                    // Method 3: Location href
                    log('📍 Method 3: Testing Location href...');
                    log('🔄 Would execute: window.location.href = "/dashboard"');
                    
                    log('✅ All redirect methods tested successfully');
                    log('🎯 In the real app, user would be redirected to dashboard');
                    
                } else {
                    log('❌ Login failed: ' + (result.message || 'Unknown error'));
                }
                
            } catch (error) {
                log('❌ Test error: ' + error.message);
            } finally {
                btn.disabled = false;
                btn.textContent = 'Test Login & Redirect';
            }
        }

        function testDirectRedirect() {
            log('🔄 Testing direct redirect methods...');
            
            // Test if we can detect current page
            log(`📍 Current page: ${window.location.pathname}`);
            log(`📍 Current host: ${window.location.host}`);
            
            // Test redirect capabilities
            if (window.history) {
                log('✅ History API available');
            } else {
                log('❌ History API not available');
            }
            
            if (window.location) {
                log('✅ Location API available');
            } else {
                log('❌ Location API not available');
            }
            
            // Test event dispatching
            try {
                window.dispatchEvent(new CustomEvent('testEvent'));
                log('✅ Custom events work');
            } catch (error) {
                log('❌ Custom events failed: ' + error.message);
            }
            
            log('✅ Direct redirect test completed');
        }

        // Initialize
        log('🚀 Redirect test page loaded');
        log('📋 Ready to test login redirect functionality');
    </script>
</body>
</html>
