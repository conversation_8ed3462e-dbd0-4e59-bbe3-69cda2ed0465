{"level":"error","message":"Database connection failed: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":null,"stale":false,"type":"Unknown"},"service":"salla-ecommerce-api","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\mongoose\\lib\\connection.js:816:11)\n    at NativeConnection.openUri (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\mongoose\\lib\\connection.js:791:11)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:120:18)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:136:5)","timestamp":"2025-07-31 05:59:58"}
{"level":"error","message":"❌ Database connection failed: Unknown database 'salla_ecommerce'","name":"SequelizeConnectionError","original":{"code":"ER_BAD_DB_ERROR","errno":1049,"sqlMessage":"Unknown database 'salla_ecommerce'","sqlState":"42000"},"parent":{"code":"ER_BAD_DB_ERROR","errno":1049,"sqlMessage":"Unknown database 'salla_ecommerce'","sqlState":"42000"},"service":"salla-ecommerce-api","stack":"SequelizeConnectionError: Unknown database 'salla_ecommerce'\n    at ConnectionManager.connect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\connection-manager.js:102:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ConnectionManager._connect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:222:24)\n    at async C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:174:32\n    at async ConnectionManager.getConnection (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:197:7)\n    at async C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:305:26\n    at async Sequelize.authenticate (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:457:5)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\config\\database.js:40:5)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:125:5)","timestamp":"2025-07-31 06:13:30"}
{"level":"error","message":"❌ Database connection failed: Key column 'parentId' doesn't exist in table","name":"SequelizeDatabaseError","original":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `categories` ADD INDEX `categories_parent_id` (`parentId`)","sqlMessage":"Key column 'parentId' doesn't exist in table","sqlState":"42000"},"parameters":{},"parent":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `categories` ADD INDEX `categories_parent_id` (`parentId`)","sqlMessage":"Key column 'parentId' doesn't exist in table","sqlState":"42000"},"service":"salla-ecommerce-api","sql":"ALTER TABLE `categories` ADD INDEX `categories_parent_id` (`parentId`)","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.addIndex (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Category.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\config\\database.js:45:7)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:125:5)","timestamp":"2025-07-31 06:14:08"}
{"level":"error","message":"❌ Database connection failed: Key column 'parentId' doesn't exist in table","name":"SequelizeDatabaseError","original":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `categories` ADD INDEX `categories_parent_id` (`parentId`)","sqlMessage":"Key column 'parentId' doesn't exist in table","sqlState":"42000"},"parameters":{},"parent":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `categories` ADD INDEX `categories_parent_id` (`parentId`)","sqlMessage":"Key column 'parentId' doesn't exist in table","sqlState":"42000"},"service":"salla-ecommerce-api","sql":"ALTER TABLE `categories` ADD INDEX `categories_parent_id` (`parentId`)","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.addIndex (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Category.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\config\\database.js:71:7)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:125:5)","timestamp":"2025-07-31 06:16:41"}
{"level":"error","message":"❌ Database connection failed: Key column 'isActive' doesn't exist in table","name":"SequelizeDatabaseError","original":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `categories` ADD INDEX `categories_is_active` (`isActive`)","sqlMessage":"Key column 'isActive' doesn't exist in table","sqlState":"42000"},"parameters":{},"parent":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `categories` ADD INDEX `categories_is_active` (`isActive`)","sqlMessage":"Key column 'isActive' doesn't exist in table","sqlState":"42000"},"service":"salla-ecommerce-api","sql":"ALTER TABLE `categories` ADD INDEX `categories_is_active` (`isActive`)","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.addIndex (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Category.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\config\\database.js:71:7)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:125:5)","timestamp":"2025-07-31 06:16:57"}
{"level":"error","message":"❌ Database connection failed: Failed to open the referenced table 'brands'","name":"SequelizeDatabaseError","original":{"code":"ER_FK_CANNOT_OPEN_PARENT","errno":1824,"sql":"CREATE TABLE IF NOT EXISTS `products` (`id` INTEGER auto_increment , `name` VARCHAR(200) NOT NULL, `description` TEXT NOT NULL, `short_description` VARCHAR(500), `sku` VARCHAR(100) UNIQUE, `price` DECIMAL(10,2) NOT NULL, `compare_price` DECIMAL(10,2), `cost_price` DECIMAL(10,2), `currency` VARCHAR(3) DEFAULT 'USD', `category_id` INTEGER NOT NULL, `brand_id` INTEGER, `tags` JSON, `images` JSON, `variants` JSON, `options` JSON, `inventory` JSON, `shipping` JSON, `seo` JSON, `status` ENUM('draft', 'active', 'archived') DEFAULT 'draft', `featured` TINYINT(1) DEFAULT false, `digital` TINYINT(1) DEFAULT false, `downloadable` TINYINT(1) DEFAULT false, `download_files` JSON, `merchant_id` INTEGER NOT NULL, `ratings` JSON, `sales_count` INTEGER DEFAULT 0, `views_count` INTEGER DEFAULT 0, `published_at` DATETIME, `meta_data` JSON, `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL, PRIMARY KEY (`id`), FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE, FOREIGN KEY (`brand_id`) REFERENCES `brands` (`id`), FOREIGN KEY (`merchant_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) ENGINE=InnoDB;","sqlMessage":"Failed to open the referenced table 'brands'","sqlState":"HY000"},"parameters":{},"parent":{"code":"ER_FK_CANNOT_OPEN_PARENT","errno":1824,"sql":"CREATE TABLE IF NOT EXISTS `products` (`id` INTEGER auto_increment , `name` VARCHAR(200) NOT NULL, `description` TEXT NOT NULL, `short_description` VARCHAR(500), `sku` VARCHAR(100) UNIQUE, `price` DECIMAL(10,2) NOT NULL, `compare_price` DECIMAL(10,2), `cost_price` DECIMAL(10,2), `currency` VARCHAR(3) DEFAULT 'USD', `category_id` INTEGER NOT NULL, `brand_id` INTEGER, `tags` JSON, `images` JSON, `variants` JSON, `options` JSON, `inventory` JSON, `shipping` JSON, `seo` JSON, `status` ENUM('draft', 'active', 'archived') DEFAULT 'draft', `featured` TINYINT(1) DEFAULT false, `digital` TINYINT(1) DEFAULT false, `downloadable` TINYINT(1) DEFAULT false, `download_files` JSON, `merchant_id` INTEGER NOT NULL, `ratings` JSON, `sales_count` INTEGER DEFAULT 0, `views_count` INTEGER DEFAULT 0, `published_at` DATETIME, `meta_data` JSON, `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL, PRIMARY KEY (`id`), FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE, FOREIGN KEY (`brand_id`) REFERENCES `brands` (`id`), FOREIGN KEY (`merchant_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) ENGINE=InnoDB;","sqlMessage":"Failed to open the referenced table 'brands'","sqlState":"HY000"},"service":"salla-ecommerce-api","sql":"CREATE TABLE IF NOT EXISTS `products` (`id` INTEGER auto_increment , `name` VARCHAR(200) NOT NULL, `description` TEXT NOT NULL, `short_description` VARCHAR(500), `sku` VARCHAR(100) UNIQUE, `price` DECIMAL(10,2) NOT NULL, `compare_price` DECIMAL(10,2), `cost_price` DECIMAL(10,2), `currency` VARCHAR(3) DEFAULT 'USD', `category_id` INTEGER NOT NULL, `brand_id` INTEGER, `tags` JSON, `images` JSON, `variants` JSON, `options` JSON, `inventory` JSON, `shipping` JSON, `seo` JSON, `status` ENUM('draft', 'active', 'archived') DEFAULT 'draft', `featured` TINYINT(1) DEFAULT false, `digital` TINYINT(1) DEFAULT false, `downloadable` TINYINT(1) DEFAULT false, `download_files` JSON, `merchant_id` INTEGER NOT NULL, `ratings` JSON, `sales_count` INTEGER DEFAULT 0, `views_count` INTEGER DEFAULT 0, `published_at` DATETIME, `meta_data` JSON, `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL, PRIMARY KEY (`id`), FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE, FOREIGN KEY (`brand_id`) REFERENCES `brands` (`id`), FOREIGN KEY (`merchant_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) ENGINE=InnoDB;","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.createTable (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:98:12)\n    at async Product.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:942:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\config\\database.js:71:7)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:125:5)","timestamp":"2025-07-31 06:17:22"}
{"level":"error","message":"❌ Database connection failed: Key column 'categoryId' doesn't exist in table","name":"SequelizeDatabaseError","original":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `products` ADD INDEX `products_category_id` (`categoryId`)","sqlMessage":"Key column 'categoryId' doesn't exist in table","sqlState":"42000"},"parameters":{},"parent":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `products` ADD INDEX `products_category_id` (`categoryId`)","sqlMessage":"Key column 'categoryId' doesn't exist in table","sqlState":"42000"},"service":"salla-ecommerce-api","sql":"ALTER TABLE `products` ADD INDEX `products_category_id` (`categoryId`)","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.addIndex (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Product.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\config\\database.js:71:7)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:125:5)","timestamp":"2025-07-31 06:17:51"}
{"level":"error","message":"❌ Database connection failed: Key column 'orderNumber' doesn't exist in table","name":"SequelizeDatabaseError","original":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","sqlMessage":"Key column 'orderNumber' doesn't exist in table","sqlState":"42000"},"parameters":{},"parent":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","sqlMessage":"Key column 'orderNumber' doesn't exist in table","sqlState":"42000"},"service":"salla-ecommerce-api","sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.addIndex (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Order.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\config\\database.js:71:7)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:125:5)","timestamp":"2025-07-31 06:18:21"}
{"level":"error","message":"❌ Database connection failed: Key column 'orderNumber' doesn't exist in table","name":"SequelizeDatabaseError","original":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","sqlMessage":"Key column 'orderNumber' doesn't exist in table","sqlState":"42000"},"parameters":{},"parent":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","sqlMessage":"Key column 'orderNumber' doesn't exist in table","sqlState":"42000"},"service":"salla-ecommerce-api","sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.addIndex (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Order.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\config\\database.js:71:7)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:125:5)","timestamp":"2025-07-31 06:18:45"}
{"level":"error","message":"❌ Database connection failed: Key column 'orderNumber' doesn't exist in table","name":"SequelizeDatabaseError","original":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","sqlMessage":"Key column 'orderNumber' doesn't exist in table","sqlState":"42000"},"parameters":{},"parent":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","sqlMessage":"Key column 'orderNumber' doesn't exist in table","sqlState":"42000"},"service":"salla-ecommerce-api","sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.addIndex (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Order.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\config\\database.js:71:7)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:125:5)","timestamp":"2025-07-31 06:18:59"}
{"level":"error","message":"❌ Database connection failed: Key column 'orderNumber' doesn't exist in table","name":"SequelizeDatabaseError","original":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","sqlMessage":"Key column 'orderNumber' doesn't exist in table","sqlState":"42000"},"parameters":{},"parent":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","sqlMessage":"Key column 'orderNumber' doesn't exist in table","sqlState":"42000"},"service":"salla-ecommerce-api","sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.addIndex (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Order.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\config\\database.js:71:7)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:125:5)","timestamp":"2025-07-31 06:19:15"}
{"level":"error","message":"❌ Database connection failed: Key column 'orderNumber' doesn't exist in table","name":"SequelizeDatabaseError","original":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","sqlMessage":"Key column 'orderNumber' doesn't exist in table","sqlState":"42000"},"parameters":{},"parent":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","sqlMessage":"Key column 'orderNumber' doesn't exist in table","sqlState":"42000"},"service":"salla-ecommerce-api","sql":"ALTER TABLE `orders` ADD UNIQUE INDEX `orders_order_number` (`orderNumber`)","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.addIndex (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Order.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\config\\database.js:71:7)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:125:5)","timestamp":"2025-07-31 06:19:29"}
{"level":"error","message":"❌ Database connection failed: Key column 'userId' doesn't exist in table","name":"SequelizeDatabaseError","original":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `carts` ADD INDEX `carts_user_id` (`userId`)","sqlMessage":"Key column 'userId' doesn't exist in table","sqlState":"42000"},"parameters":{},"parent":{"code":"ER_KEY_COLUMN_DOES_NOT_EXITS","errno":1072,"sql":"ALTER TABLE `carts` ADD INDEX `carts_user_id` (`userId`)","sqlMessage":"Key column 'userId' doesn't exist in table","sqlState":"42000"},"service":"salla-ecommerce-api","sql":"ALTER TABLE `carts` ADD INDEX `carts_user_id` (`userId`)","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.addIndex (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:250:12)\n    at async Cart.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:999:7)\n    at async Sequelize.sync (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async connectDB (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\config\\database.js:71:7)\n    at async startServer (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\app.js:125:5)","timestamp":"2025-07-31 06:19:43"}
{"level":"error","message":"Get categories error: Category.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Category.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\categories.js:77:39\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at optionalAuth (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:74:5)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-31 06:20:58"}
{"level":"error","message":"Get categories error: Category.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Category.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\categories.js:77:39\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at optionalAuth (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:74:5)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-31 06:25:11"}
{"level":"error","message":"Get categories error: Category.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Category.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\categories.js:77:39\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at optionalAuth (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:74:5)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-31 06:25:21"}
{"level":"error","message":"Get cart error: cart.populate is not a function","service":"salla-ecommerce-api","stack":"TypeError: cart.populate is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\cart.js:66:16\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 06:27:23"}
{"level":"error","message":"Create category error: category.populate is not a function","service":"salla-ecommerce-api","stack":"TypeError: category.populate is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\categories.js:255:20\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 06:28:39"}
{"errors":[{"instance":{"attributes":[],"createdAt":"2025-07-31T03:29:11.506Z","description":"Electronic devices and gadgets","id":null,"image":null,"isActive":true,"isFeatured":true,"level":0,"metaData":{},"name":"Electronics","position":0,"seo":{"description":null,"keywords":[],"title":null},"slug":"electronics","updatedAt":"2025-07-31T03:29:11.506Z"},"message":"slug must be unique","origin":"DB","path":"slug","type":"unique violation","validatorArgs":[],"validatorKey":"not_unique","validatorName":null,"value":"electronics"}],"fields":{"slug":"electronics"},"level":"error","message":"Create category error: Validation error","name":"SequelizeUniqueConstraintError","original":{"code":"ER_DUP_ENTRY","errno":1062,"parameters":["Electronics","Electronic devices and gadgets","electronics",null,0,0,true,true,"{\"title\":null,\"description\":null,\"keywords\":[]}","[]","{}","2025-07-31 03:29:11","2025-07-31 03:29:11"],"sql":"INSERT INTO `categories` (`id`,`name`,`description`,`slug`,`image`,`level`,`position`,`is_active`,`is_featured`,`seo`,`attributes`,`meta_data`,`created_at`,`updated_at`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?,?,?,?);","sqlMessage":"Duplicate entry 'electronics' for key 'categories.slug'","sqlState":"23000"},"parent":{"code":"ER_DUP_ENTRY","errno":1062,"parameters":["Electronics","Electronic devices and gadgets","electronics",null,0,0,true,true,"{\"title\":null,\"description\":null,\"keywords\":[]}","[]","{}","2025-07-31 03:29:11","2025-07-31 03:29:11"],"sql":"INSERT INTO `categories` (`id`,`name`,`description`,`slug`,`image`,`level`,`position`,`is_active`,`is_featured`,`seo`,`attributes`,`meta_data`,`created_at`,`updated_at`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?,?,?,?);","sqlMessage":"Duplicate entry 'electronics' for key 'categories.slug'","sqlState":"23000"},"service":"salla-ecommerce-api","sql":"INSERT INTO `categories` (`id`,`name`,`description`,`slug`,`image`,`level`,`position`,`is_active`,`is_featured`,`seo`,`attributes`,`meta_data`,`created_at`,`updated_at`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?,?,?,?);","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.insert (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async Category.create (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\categories.js:254:22","timestamp":"2025-07-31 06:29:11"}
{"errors":[{"instance":{"attributes":[],"createdAt":"2025-07-31T03:29:53.111Z","description":"Electronic devices and gadgets","id":null,"image":null,"isActive":true,"isFeatured":true,"level":0,"metaData":{},"name":"Electronics","position":0,"seo":{"description":null,"keywords":[],"title":null},"slug":"electronics","updatedAt":"2025-07-31T03:29:53.111Z"},"message":"slug must be unique","origin":"DB","path":"slug","type":"unique violation","validatorArgs":[],"validatorKey":"not_unique","validatorName":null,"value":"electronics"}],"fields":{"slug":"electronics"},"level":"error","message":"Create category error: Validation error","name":"SequelizeUniqueConstraintError","original":{"code":"ER_DUP_ENTRY","errno":1062,"parameters":["Electronics","Electronic devices and gadgets","electronics",null,0,0,true,true,"{\"title\":null,\"description\":null,\"keywords\":[]}","[]","{}","2025-07-31 03:29:53","2025-07-31 03:29:53"],"sql":"INSERT INTO `categories` (`id`,`name`,`description`,`slug`,`image`,`level`,`position`,`is_active`,`is_featured`,`seo`,`attributes`,`meta_data`,`created_at`,`updated_at`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?,?,?,?);","sqlMessage":"Duplicate entry 'electronics' for key 'categories.slug'","sqlState":"23000"},"parent":{"code":"ER_DUP_ENTRY","errno":1062,"parameters":["Electronics","Electronic devices and gadgets","electronics",null,0,0,true,true,"{\"title\":null,\"description\":null,\"keywords\":[]}","[]","{}","2025-07-31 03:29:53","2025-07-31 03:29:53"],"sql":"INSERT INTO `categories` (`id`,`name`,`description`,`slug`,`image`,`level`,`position`,`is_active`,`is_featured`,`seo`,`attributes`,`meta_data`,`created_at`,`updated_at`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?,?,?,?);","sqlMessage":"Duplicate entry 'electronics' for key 'categories.slug'","sqlState":"23000"},"service":"salla-ecommerce-api","sql":"INSERT INTO `categories` (`id`,`name`,`description`,`slug`,`image`,`level`,`position`,`is_active`,`is_featured`,`seo`,`attributes`,`meta_data`,`created_at`,`updated_at`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?,?,?,?);","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.insert (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async Category.create (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\categories.js:254:22","timestamp":"2025-07-31 06:29:53"}
{"level":"error","message":"Get customers error: User.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: User.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\customers.js:66:34\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:53:5\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 06:30:12"}
{"errors":[{"instance":{"attributes":[],"createdAt":"2025-07-31T03:31:43.328Z","description":"Electronic devices and gadgets","id":null,"image":null,"isActive":true,"isFeatured":true,"level":0,"metaData":{},"name":"Electronics","position":0,"seo":{"description":null,"keywords":[],"title":null},"slug":"electronics","updatedAt":"2025-07-31T03:31:43.328Z"},"message":"slug must be unique","origin":"DB","path":"slug","type":"unique violation","validatorArgs":[],"validatorKey":"not_unique","validatorName":null,"value":"electronics"}],"fields":{"slug":"electronics"},"level":"error","message":"Create category error: Validation error","name":"SequelizeUniqueConstraintError","original":{"code":"ER_DUP_ENTRY","errno":1062,"parameters":["Electronics","Electronic devices and gadgets","electronics",null,0,0,true,true,"{\"title\":null,\"description\":null,\"keywords\":[]}","[]","{}","2025-07-31 03:31:43","2025-07-31 03:31:43"],"sql":"INSERT INTO `categories` (`id`,`name`,`description`,`slug`,`image`,`level`,`position`,`is_active`,`is_featured`,`seo`,`attributes`,`meta_data`,`created_at`,`updated_at`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?,?,?,?);","sqlMessage":"Duplicate entry 'electronics' for key 'categories.slug'","sqlState":"23000"},"parent":{"code":"ER_DUP_ENTRY","errno":1062,"parameters":["Electronics","Electronic devices and gadgets","electronics",null,0,0,true,true,"{\"title\":null,\"description\":null,\"keywords\":[]}","[]","{}","2025-07-31 03:31:43","2025-07-31 03:31:43"],"sql":"INSERT INTO `categories` (`id`,`name`,`description`,`slug`,`image`,`level`,`position`,`is_active`,`is_featured`,`seo`,`attributes`,`meta_data`,`created_at`,`updated_at`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?,?,?,?);","sqlMessage":"Duplicate entry 'electronics' for key 'categories.slug'","sqlState":"23000"},"service":"salla-ecommerce-api","sql":"INSERT INTO `categories` (`id`,`name`,`description`,`slug`,`image`,`level`,`position`,`is_active`,`is_featured`,`seo`,`attributes`,`meta_data`,`created_at`,`updated_at`) VALUES (DEFAULT,?,?,?,?,?,?,?,?,?,?,?,?,?);","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.insert (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:308:21)\n    at async model.save (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:2490:35)\n    at async Category.create (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:1362:12)\n    at async C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\categories.js:254:22","timestamp":"2025-07-31 06:31:43"}
{"level":"error","message":"Get cart error: cart.isModified is not a function","service":"salla-ecommerce-api","stack":"TypeError: cart.isModified is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\cart.js:72:14\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 06:37:43"}
{"level":"error","message":"Create coupon error: coupons is not defined","service":"salla-ecommerce-api","stack":"ReferenceError: coupons is not defined\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\coupons.js:298:28\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 06:39:10"}
{"level":"error","message":"Get sales data error: Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'salla_ecommerce.Order.created_at' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by","name":"SequelizeDatabaseError","original":{"code":"ER_WRONG_FIELD_WITH_GROUP","errno":1055,"sql":"SELECT DATE_FORMAT(`created_at`, '%a') AS `name`, SUM(`total_amount`) AS `sales`, COUNT(`id`) AS `orders` FROM `orders` AS `Order` WHERE `Order`.`created_at` >= '2025-07-24 03:56:07' AND `Order`.`payment_status` = 'paid' GROUP BY DATE(created_at) ORDER BY `created_at` ASC;","sqlMessage":"Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'salla_ecommerce.Order.created_at' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by","sqlState":"42000"},"parameters":{},"parent":{"code":"ER_WRONG_FIELD_WITH_GROUP","errno":1055,"sql":"SELECT DATE_FORMAT(`created_at`, '%a') AS `name`, SUM(`total_amount`) AS `sales`, COUNT(`id`) AS `orders` FROM `orders` AS `Order` WHERE `Order`.`created_at` >= '2025-07-24 03:56:07' AND `Order`.`payment_status` = 'paid' GROUP BY DATE(created_at) ORDER BY `created_at` ASC;","sqlMessage":"Expression #1 of SELECT list is not in GROUP BY clause and contains nonaggregated column 'salla_ecommerce.Order.created_at' which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by","sqlState":"42000"},"service":"salla-ecommerce-api","sql":"SELECT DATE_FORMAT(`created_at`, '%a') AS `name`, SUM(`total_amount`) AS `sales`, COUNT(`id`) AS `orders` FROM `orders` AS `Order` WHERE `Order`.`created_at` >= '2025-07-24 03:56:07' AND `Order`.`payment_status` = 'paid' GROUP BY DATE(created_at) ORDER BY `created_at` ASC;","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.select (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:407:12)\n    at async Order.findAll (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:1140:21)\n    at async C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\analytics.js:256:23","timestamp":"2025-07-31 06:56:07"}
{"level":"error","message":"Get dashboard stats error: Unknown column 'Order.store_id' in 'where clause'","name":"SequelizeDatabaseError","original":{"code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"SELECT sum(`total_amount`) AS `sum` FROM `orders` AS `Order` WHERE `Order`.`store_id` = 1 AND `Order`.`payment_status` = 'paid' AND `Order`.`created_at` >= '2024-07-30 21:00:00';","sqlMessage":"Unknown column 'Order.store_id' in 'where clause'","sqlState":"42S22"},"parameters":{},"parent":{"code":"ER_BAD_FIELD_ERROR","errno":1054,"sql":"SELECT sum(`total_amount`) AS `sum` FROM `orders` AS `Order` WHERE `Order`.`store_id` = 1 AND `Order`.`payment_status` = 'paid' AND `Order`.`created_at` >= '2024-07-30 21:00:00';","sqlMessage":"Unknown column 'Order.store_id' in 'where clause'","sqlState":"42S22"},"service":"salla-ecommerce-api","sql":"SELECT sum(`total_amount`) AS `sum` FROM `orders` AS `Order` WHERE `Order`.`store_id` = 1 AND `Order`.`payment_status` = 'paid' AND `Order`.`created_at` >= '2024-07-30 21:00:00';","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\mysql\\query.js:52:25)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async MySQLQueryInterface.rawSelect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:434:18)\n    at async Order.aggregate (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:1277:19)\n    at async Order.sum (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\sequelize\\lib\\model.js:1338:12)\n    at async C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\analytics.js:47:26","timestamp":"2025-07-31 07:20:02"}
{"level":"error","message":"Get dashboard stats error: lastMonthOrders is not defined","service":"salla-ecommerce-api","stack":"ReferenceError: lastMonthOrders is not defined\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\analytics.js:105:20\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 07:39:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:26"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:26"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:26"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:26"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:26"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:43"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:43"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:43"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:43"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:43"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:57"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:57"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:57"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:57"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:44:57"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:45:34"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:45:34"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:45:34"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:45:34"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:45:34"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:25"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:25"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:25"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:25"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:25"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:30"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:30"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:30"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:30"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:30"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:35"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:35"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:35"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:35"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:35"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:42"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:42"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:42"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:42"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:42"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:45"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:45"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:45"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:45"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:45"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:49"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:49"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:49"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:49"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:46:49"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:03"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:03"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:03"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:03"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:04"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:07"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:07"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:07"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:07"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:07"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:11"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:11"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:11"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:11"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:11"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:21"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:21"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:21"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:21"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:47:21"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:56:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:56:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:56:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:56:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:56:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:56:24"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:56:24"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:56:24"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:56:24"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:56:24"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:00"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:00"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:00"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:00"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:00"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:14"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:14"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:14"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:14"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:14"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:22"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:22"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:22"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:22"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:22"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:55"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:55"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:55"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:55"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:55"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:56"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:56"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:56"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:56"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 07:57:56"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 08:09:33"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 08:09:33"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 08:09:33"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 08:09:33"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 08:09:33"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 08:12:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 08:12:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 08:12:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 08:12:17"}
{"level":"error","message":"Authentication error: jwt malformed","name":"JsonWebTokenError","service":"salla-ecommerce-api","stack":"JsonWebTokenError: jwt malformed\n    at module.exports [as verify] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\jsonwebtoken\\verify.js:70:17)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:20:25)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\index.js:47:12)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)","timestamp":"2025-07-31 08:12:17"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:20:04"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:20:05"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:20:07"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:26:33"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:26:34"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:26:36"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:28:04"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:28:05"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:28:18"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:30:35"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:30:36"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:30:38"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:33:17"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:33:18"}
{"level":"error","message":"Get orders error: Order.find is not a function","service":"salla-ecommerce-api","stack":"TypeError: Order.find is not a function\n    at C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\routes\\orders.js:90:32\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at protect (C:\\Users\\<USER>\\WebstormProjects\\api_app_tok\\src\\middleware\\auth.js:40:5)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-31 08:33:20"}
