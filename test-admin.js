// Test admin functionality
const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

// Test admin registration
async function testAdminRegister() {
  try {
    console.log('🧪 Testing admin registration...');
    
    const userData = {
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'AdminPass123',
      passwordConfirm: 'AdminPass123',
      phone: '+966501234568',
      role: 'admin'
    };

    const response = await axios.post(`${API_BASE}/auth/register`, userData);
    
    console.log('✅ Admin registration successful!');
    console.log('Admin:', response.data.data.user);
    
    return response.data.token;
  } catch (error) {
    console.log('❌ Admin registration failed:');
    console.log('Error:', error.response?.data?.message || error.message);
    return null;
  }
}

// Test admin login
async function testAdminLogin() {
  try {
    console.log('\n🧪 Testing admin login...');
    
    const loginData = {
      email: '<EMAIL>',
      password: 'AdminPass123'
    };

    const response = await axios.post(`${API_BASE}/auth/login`, loginData);
    
    console.log('✅ Admin login successful!');
    console.log('Admin role:', response.data.data.user.role);
    
    return response.data.token;
  } catch (error) {
    console.log('❌ Admin login failed:');
    console.log('Error:', error.response?.data?.message || error.message);
    return null;
  }
}

// Test create category as admin
async function testCreateCategory(token) {
  try {
    console.log('\n🧪 Testing create category as admin...');
    
    const categoryData = {
      name: 'Electronics',
      description: 'Electronic devices and gadgets',
      isActive: true,
      isFeatured: true
    };

    const response = await axios.post(`${API_BASE}/categories`, categoryData, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Category created successfully!');
    console.log('Category:', response.data.data.category);
    
    return response.data.data.category;
  } catch (error) {
    console.log('❌ Category creation failed:');
    console.log('Error:', error.response?.data?.message || error.message);
    return null;
  }
}

// Test create product as admin
async function testCreateProduct(token, categoryId) {
  try {
    console.log('\n🧪 Testing create product as admin...');
    
    const productData = {
      name: 'iPhone 15 Pro',
      description: 'Latest iPhone with advanced features',
      shortDescription: 'Premium smartphone with Pro camera system',
      price: 999.99,
      comparePrice: 1099.99,
      categoryId: categoryId,
      status: 'active',
      featured: true,
      inventory: {
        quantity: 50,
        trackQuantity: true,
        allowBackorder: false
      },
      images: [
        {
          url: 'https://example.com/iphone15pro.jpg',
          alt: 'iPhone 15 Pro',
          isMain: true
        }
      ],
      tags: ['smartphone', 'apple', 'premium']
    };

    const response = await axios.post(`${API_BASE}/products`, productData, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    console.log('✅ Product created successfully!');
    console.log('Product:', response.data.data.product);
    
    return response.data.data.product;
  } catch (error) {
    console.log('❌ Product creation failed:');
    console.log('Error:', error.response?.data?.message || error.message);
    return null;
  }
}

// Test get products
async function testGetProducts() {
  try {
    console.log('\n🧪 Testing get products...');
    
    const response = await axios.get(`${API_BASE}/products`);
    
    console.log('✅ Products retrieved successfully!');
    console.log('Products count:', response.data.data.products.length);
    console.log('First product:', response.data.data.products[0]);
    
    return response.data.data.products;
  } catch (error) {
    console.log('❌ Products retrieval failed:');
    console.log('Error:', error.response?.data?.message || error.message);
    return null;
  }
}

// Run admin tests
async function runAdminTests() {
  console.log('🚀 Starting Admin Tests...\n');
  
  // Test admin registration
  let token = await testAdminRegister();
  
  // If registration fails, try login
  if (!token) {
    token = await testAdminLogin();
  }
  
  if (token) {
    // Test category creation
    const category = await testCreateCategory(token);
    
    if (category) {
      // Test product creation
      await testCreateProduct(token, category.id);
    }
  }
  
  // Test get products (public endpoint)
  await testGetProducts();
  
  console.log('\n🏁 Admin tests completed!');
}

// Run the tests
runAdminTests().catch(console.error);
