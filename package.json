{"name": "salla-ecommerce-api", "version": "1.0.0", "description": "Advanced E-commerce API similar to Salla platform", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "migrate": "npx sequelize-cli db:migrate", "seed": "npx sequelize-cli db:seed:all"}, "keywords": ["ecommerce", "api", "salla", "nodejs", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "sequelize": "^6.32.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.40.0", "stripe": "^13.5.0", "nodemailer": "^6.9.4", "socket.io": "^4.7.2", "redis": "^4.6.8", "winston": "^3.10.0", "dotenv": "^16.3.1", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "moment": "^2.29.4", "uuid": "^9.0.0", "axios": "^1.5.0", "joi": "^17.9.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3", "@types/jest": "^29.5.5", "sequelize-cli": "^6.6.1"}}