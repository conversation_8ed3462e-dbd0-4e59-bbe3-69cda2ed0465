// Generate unique Saudi phone numbers for testing
function generateSaudiPhone() {
  // Saudi mobile prefixes: 50, 51, 52, 53, 54, 55, 56, 57, 58, 59
  const prefixes = ['50', '51', '52', '53', '54', '55', '56', '57', '58', '59'];
  const randomPrefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  
  // Generate 7 random digits
  let randomDigits = '';
  for (let i = 0; i < 7; i++) {
    randomDigits += Math.floor(Math.random() * 10);
  }
  
  return `+966${randomPrefix}${randomDigits}`;
}

// Generate multiple unique phone numbers
function generateMultipleSaudiPhones(count = 10) {
  const phones = new Set();
  
  while (phones.size < count) {
    phones.add(generateSaudiPhone());
  }
  
  return Array.from(phones);
}

// Test the generator
console.log('🇸🇦 Generated Saudi Phone Numbers for Testing:\n');

const testPhones = generateMultipleSaudiPhones(10);
testPhones.forEach((phone, index) => {
  console.log(`${index + 1}. ${phone}`);
});

console.log('\n📋 Copy any of these phone numbers for registration testing.');
console.log('✅ All numbers follow the format: +966[5X][XXXXXXX]');
console.log('✅ All numbers are unique and valid Saudi mobile numbers.');

// Export for use in other files
module.exports = {
  generateSaudiPhone,
  generateMultipleSaudiPhones
};
