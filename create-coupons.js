// Create sample coupons
const { Coupon } = require('./src/models');
const { connectDB } = require('./src/config/database');

async function createCoupons() {
  try {
    // Connect to database
    await connectDB();
    
    console.log('🔗 Connected to database');
    
    // Sample coupons
    const couponsData = [
      {
        code: 'WELCOME10',
        type: 'percentage',
        value: 10,
        description: 'Welcome discount - 10% off your first order',
        minimumAmount: 50,
        maximumDiscount: 20,
        usageLimit: 1000,
        userLimit: 1,
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        active: true,
        firstTimeCustomersOnly: true
      },
      {
        code: 'SAVE20',
        type: 'fixed',
        value: 20,
        description: 'Save $20 on orders over $100',
        minimumAmount: 100,
        usageLimit: 500,
        userLimit: 3,
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        active: true,
        firstTimeCustomersOnly: false
      },
      {
        code: 'FREESHIP',
        type: 'free_shipping',
        value: 0,
        description: 'Free shipping on all orders',
        minimumAmount: 0,
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        active: true,
        firstTimeCustomersOnly: false
      },
      {
        code: 'SUMMER25',
        type: 'percentage',
        value: 25,
        description: 'Summer sale - 25% off everything',
        minimumAmount: 75,
        maximumDiscount: 50,
        usageLimit: 200,
        userLimit: 1,
        startDate: '2024-06-01',
        endDate: '2024-08-31',
        active: true,
        firstTimeCustomersOnly: false
      },
      {
        code: 'ELECTRONICS15',
        type: 'percentage',
        value: 15,
        description: '15% off electronics category',
        minimumAmount: 100,
        maximumDiscount: 100,
        usageLimit: 300,
        userLimit: 2,
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        active: true,
        firstTimeCustomersOnly: false,
        applicableCategories: [1] // Electronics category
      }
    ];
    
    // Create coupons
    for (const couponData of couponsData) {
      const existingCoupon = await Coupon.findOne({ where: { code: couponData.code } });
      
      if (!existingCoupon) {
        const coupon = await Coupon.create(couponData);
        console.log('✅ Coupon created:', coupon.code);
      } else {
        console.log('✅ Coupon already exists:', existingCoupon.code);
      }
    }
    
    console.log('\n🎉 Sample coupons created successfully!');
    
    // Display all coupons
    const allCoupons = await Coupon.findAll({
      order: [['createdAt', 'DESC']]
    });
    
    console.log('\n📋 All Coupons:');
    allCoupons.forEach(coupon => {
      console.log(`- ${coupon.code}: ${coupon.description} (${coupon.type})`);
    });
    
  } catch (error) {
    console.error('❌ Error creating coupons:', error);
  } finally {
    process.exit(0);
  }
}

createCoupons();
