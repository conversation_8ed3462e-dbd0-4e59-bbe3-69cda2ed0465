// Create test data directly in database
const { User, Category, Product } = require('./src/models');
const { connectDB } = require('./src/config/database');

async function createTestData() {
  try {
    // Connect to database
    await connectDB();
    
    console.log('🔗 Connected to database');
    
    // Create category
    let category = await Category.findOne({ where: { name: 'Electronics' } });
    
    if (!category) {
      category = await Category.create({
        name: 'Electronics',
        description: 'Electronic devices and gadgets',
        slug: 'electronics',
        isActive: true,
        isFeatured: true,
        level: 0,
        position: 1
      });
      
      console.log('✅ Category created:', category.toJSON());
    } else {
      console.log('✅ Category already exists:', category.toJSON());
    }
    
    // Get admin user
    const admin = await User.findOne({ where: { role: 'admin' } });
    
    if (!admin) {
      console.log('❌ No admin user found');
      return;
    }
    
    // Create product
    let product = await Product.findOne({ where: { name: 'iPhone 15 Pro' } });
    
    if (!product) {
      product = await Product.create({
        name: 'iPhone 15 Pro',
        description: 'Latest iPhone with advanced features and Pro camera system',
        shortDescription: 'Premium smartphone with Pro camera system',
        price: 999.99,
        comparePrice: 1099.99,
        categoryId: category.id,
        merchantId: admin.id,
        status: 'active',
        featured: true,
        inventory: {
          quantity: 50,
          trackQuantity: true,
          allowBackorder: false,
          lowStockThreshold: 5
        },
        images: [
          {
            url: 'https://example.com/iphone15pro.jpg',
            alt: 'iPhone 15 Pro',
            isMain: true
          }
        ],
        tags: ['smartphone', 'apple', 'premium'],
        seo: {
          title: 'iPhone 15 Pro - Premium Smartphone',
          description: 'Get the latest iPhone 15 Pro with advanced features',
          keywords: ['iphone', 'smartphone', 'apple'],
          slug: 'iphone-15-pro'
        }
      });
      
      console.log('✅ Product created:', product.toJSON());
    } else {
      console.log('✅ Product already exists:', product.toJSON());
    }
    
    console.log('\n🎉 Test data created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating test data:', error);
  } finally {
    process.exit(0);
  }
}

createTestData();
