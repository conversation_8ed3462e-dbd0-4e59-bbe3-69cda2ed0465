// Test login with email or phone
const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

async function testEmailPhoneLogin() {
  try {
    console.log('🧪 Testing Email & Phone Login...\n');
    
    // First, create a test user with both email and phone
    console.log('1. Creating test user with email and phone...');
    const timestamp = Date.now();
    const testUser = {
      name: 'Test User',
      email: `test${timestamp}@example.com`,
      password: 'TestPass123',
      passwordConfirm: 'TestPass123',
      phone: `+966${Math.floor(Math.random() * 900000000) + 500000000}`, // Random Saudi phone
      role: 'admin',
      storeName: `Test Store ${timestamp}`,
      storeDomain: `test-${timestamp}`,
      storeDescription: 'Test store for login testing'
    };
    
    try {
      const registerResponse = await axios.post(`${API_BASE}/auth/register`, testUser);
      console.log('✅ Test user created successfully');
      console.log(`   Email: ${testUser.email}`);
      console.log(`   Phone: ${testUser.phone}`);
    } catch (error) {
      if (error.response?.status === 409) {
        console.log('ℹ️  User already exists, proceeding with login tests...');
      } else {
        throw error;
      }
    }
    
    // Test 1: Login with email
    console.log('\n2. Testing login with EMAIL...');
    try {
      const emailLoginResponse = await axios.post(`${API_BASE}/auth/login`, {
        email: testUser.email,
        password: testUser.password
      });
      
      if (emailLoginResponse.data.success) {
        console.log('✅ Email login successful');
        console.log(`   User: ${emailLoginResponse.data.data.user.name}`);
        console.log(`   Token: ${emailLoginResponse.data.token ? 'Received' : 'Missing'}`);
      }
    } catch (error) {
      console.log('❌ Email login failed:', error.response?.data?.message || error.message);
    }
    
    // Test 2: Login with phone
    console.log('\n3. Testing login with PHONE...');
    try {
      const phoneLoginResponse = await axios.post(`${API_BASE}/auth/login`, {
        phone: testUser.phone,
        password: testUser.password
      });
      
      if (phoneLoginResponse.data.success) {
        console.log('✅ Phone login successful');
        console.log(`   User: ${phoneLoginResponse.data.data.user.name}`);
        console.log(`   Token: ${phoneLoginResponse.data.token ? 'Received' : 'Missing'}`);
      }
    } catch (error) {
      console.log('❌ Phone login failed:', error.response?.data?.message || error.message);
    }
    
    // Test 3: Login with emailOrPhone field (email)
    console.log('\n4. Testing login with EMAIL using emailOrPhone field...');
    try {
      const emailOrPhoneLoginResponse = await axios.post(`${API_BASE}/auth/login`, {
        emailOrPhone: testUser.email,
        password: testUser.password
      });
      
      if (emailOrPhoneLoginResponse.data.success) {
        console.log('✅ EmailOrPhone (email) login successful');
        console.log(`   User: ${emailOrPhoneLoginResponse.data.data.user.name}`);
        console.log(`   Token: ${emailOrPhoneLoginResponse.data.token ? 'Received' : 'Missing'}`);
      }
    } catch (error) {
      console.log('❌ EmailOrPhone (email) login failed:', error.response?.data?.message || error.message);
    }
    
    // Test 4: Login with emailOrPhone field (phone)
    console.log('\n5. Testing login with PHONE using emailOrPhone field...');
    try {
      const phoneOrEmailLoginResponse = await axios.post(`${API_BASE}/auth/login`, {
        emailOrPhone: testUser.phone,
        password: testUser.password
      });
      
      if (phoneOrEmailLoginResponse.data.success) {
        console.log('✅ EmailOrPhone (phone) login successful');
        console.log(`   User: ${phoneOrEmailLoginResponse.data.data.user.name}`);
        console.log(`   Token: ${phoneOrEmailLoginResponse.data.token ? 'Received' : 'Missing'}`);
      }
    } catch (error) {
      console.log('❌ EmailOrPhone (phone) login failed:', error.response?.data?.message || error.message);
    }
    
    // Test 5: Test with existing admin user
    console.log('\n6. Testing with existing admin user...');
    try {
      const adminLoginResponse = await axios.post(`${API_BASE}/auth/login`, {
        emailOrPhone: '<EMAIL>',
        password: 'AdminPass123'
      });
      
      if (adminLoginResponse.data.success) {
        console.log('✅ Admin login with emailOrPhone successful');
        console.log(`   User: ${adminLoginResponse.data.data.user.name}`);
      }
    } catch (error) {
      console.log('❌ Admin login failed:', error.response?.data?.message || error.message);
    }
    
    console.log('\n🎉 Email & Phone Login Tests Completed!');
    console.log('\n📋 Summary:');
    console.log('✅ Users can now login with:');
    console.log('   - Email address (e.g., <EMAIL>)');
    console.log('   - Saudi phone number (e.g., +966501234567)');
    console.log('   - Mixed field that accepts both');
    console.log('\n🌐 Frontend Usage:');
    console.log('- Open http://localhost:3000');
    console.log('- Use "Email or Phone Number" field');
    console.log('- Enter either email or +966XXXXXXXXX format');
    console.log('- System will automatically detect the format');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
  }
}

testEmailPhoneLogin();
