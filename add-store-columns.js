// Add store_id columns to existing tables
const { sequelize } = require('./src/models');

async function addStoreColumns() {
  try {
    console.log('🔄 Adding store_id columns to existing tables...\n');

    // Add store_id to categories table
    try {
      await sequelize.query(`
        ALTER TABLE categories 
        ADD COLUMN store_id INT NOT NULL DEFAULT 1,
        ADD FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE
      `);
      console.log('✅ Added store_id to categories table');
    } catch (error) {
      if (error.original?.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  store_id already exists in categories table');
      } else {
        console.log('❌ Error adding store_id to categories:', error.message);
      }
    }

    // Add store_id to products table
    try {
      await sequelize.query(`
        ALTER TABLE products 
        ADD COLUMN store_id INT NOT NULL DEFAULT 1,
        ADD FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE
      `);
      console.log('✅ Added store_id to products table');
    } catch (error) {
      if (error.original?.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  store_id already exists in products table');
      } else {
        console.log('❌ Error adding store_id to products:', error.message);
      }
    }

    // Add store_id to orders table
    try {
      await sequelize.query(`
        ALTER TABLE orders 
        ADD COLUMN store_id INT NOT NULL DEFAULT 1,
        ADD FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE
      `);
      console.log('✅ Added store_id to orders table');
    } catch (error) {
      if (error.original?.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  store_id already exists in orders table');
      } else {
        console.log('❌ Error adding store_id to orders:', error.message);
      }
    }

    // Add store_id to coupons table
    try {
      await sequelize.query(`
        ALTER TABLE coupons 
        ADD COLUMN store_id INT NOT NULL DEFAULT 1,
        ADD FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE
      `);
      console.log('✅ Added store_id to coupons table');
    } catch (error) {
      if (error.original?.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  store_id already exists in coupons table');
      } else {
        console.log('❌ Error adding store_id to coupons:', error.message);
      }
    }

    // Update existing data to link with admin store
    console.log('\n🔄 Linking existing data to admin store...');
    
    // Find admin user's store
    const [adminStore] = await sequelize.query(`
      SELECT s.id FROM stores s 
      JOIN users u ON s.owner_id = u.id 
      WHERE u.email = '<EMAIL>' 
      LIMIT 1
    `);

    if (adminStore.length > 0) {
      const storeId = adminStore[0].id;
      console.log(`Found admin store with ID: ${storeId}`);

      // Update existing records
      await sequelize.query(`UPDATE categories SET store_id = ${storeId} WHERE store_id = 1`);
      await sequelize.query(`UPDATE products SET store_id = ${storeId} WHERE store_id = 1`);
      await sequelize.query(`UPDATE orders SET store_id = ${storeId} WHERE store_id = 1`);
      await sequelize.query(`UPDATE coupons SET store_id = ${storeId} WHERE store_id = 1`);
      
      console.log('✅ Updated existing data to link with admin store');
    } else {
      console.log('ℹ️  No admin store found, keeping default values');
    }

    console.log('\n🎉 Database migration completed successfully!');
    console.log('\n📋 Summary:');
    console.log('- Added store_id to categories ✅');
    console.log('- Added store_id to products ✅');
    console.log('- Added store_id to orders ✅');
    console.log('- Added store_id to coupons ✅');
    console.log('- Linked existing data to admin store ✅');

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await sequelize.close();
  }
}

addStoreColumns();
