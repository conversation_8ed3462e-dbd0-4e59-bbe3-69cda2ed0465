<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login & Redirect</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>Test Login & Redirect</h1>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="AdminPass123" required>
            </div>
            
            <button type="submit" id="submitBtn">Login & Test Redirect</button>
        </form>
        
        <div id="result"></div>
        
        <div class="result info" style="margin-top: 30px;">
            <h3>📋 Test Steps:</h3>
            <ol>
                <li>Click "Login & Test Redirect"</li>
                <li>Watch the console for detailed logs</li>
                <li>Check if redirect happens automatically</li>
                <li>If successful, you should see dashboard data</li>
            </ol>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            
            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.textContent = 'Logging in...';
            resultDiv.innerHTML = '';
            
            // Get form data
            const formData = new FormData(this);
            const data = {
                email: formData.get('email'),
                password: formData.get('password')
            };
            
            try {
                console.log('🔄 Starting login test...');
                
                // Step 1: Login
                const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const loginResult = await loginResponse.json();
                console.log('Login response:', loginResult);
                
                if (loginResponse.ok && loginResult.success) {
                    console.log('✅ Login successful');
                    
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Login Successful!</h3>
                            <p><strong>User:</strong> ${loginResult.data.user.name}</p>
                            <p><strong>Email:</strong> ${loginResult.data.user.email}</p>
                            <p><strong>Role:</strong> ${loginResult.data.user.role}</p>
                            <p><strong>Token:</strong> ${loginResult.token ? 'Received' : 'Missing'}</p>
                        </div>
                    `;
                    
                    // Step 2: Test dashboard access
                    console.log('🔄 Testing dashboard access...');
                    
                    try {
                        const dashboardResponse = await fetch('http://localhost:3001/api/analytics/dashboard', {
                            headers: {
                                'Authorization': `Bearer ${loginResult.token}`
                            }
                        });
                        
                        const dashboardResult = await dashboardResponse.json();
                        console.log('Dashboard response:', dashboardResult);
                        
                        if (dashboardResponse.ok) {
                            resultDiv.innerHTML += `
                                <div class="result success" style="margin-top: 10px;">
                                    <h3>✅ Dashboard Access Successful!</h3>
                                    <p>Analytics data retrieved successfully</p>
                                    <p><strong>Stats Count:</strong> ${dashboardResult.data?.stats?.length || 0}</p>
                                </div>
                            `;
                            
                            // Step 3: Simulate redirect
                            console.log('🔄 Simulating redirect to dashboard...');
                            
                            setTimeout(() => {
                                resultDiv.innerHTML += `
                                    <div class="result info" style="margin-top: 10px;">
                                        <h3>🔄 Redirect Test</h3>
                                        <p>In the actual app, you would now be redirected to:</p>
                                        <p><strong>http://localhost:3000/dashboard</strong></p>
                                        <p>Open the actual app to test the full flow!</p>
                                    </div>
                                `;
                            }, 1000);
                            
                        } else {
                            throw new Error('Dashboard access failed');
                        }
                        
                    } catch (dashError) {
                        console.error('Dashboard test failed:', dashError);
                        resultDiv.innerHTML += `
                            <div class="result error" style="margin-top: 10px;">
                                <h3>❌ Dashboard Access Failed</h3>
                                <p>${dashError.message}</p>
                            </div>
                        `;
                    }
                    
                } else {
                    throw new Error(loginResult.message || 'Login failed');
                }
                
            } catch (error) {
                console.error('Login test failed:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Login Failed</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Login & Test Redirect';
            }
        });
    </script>
</body>
</html>
