// Simple test for email/phone login
const axios = require('axios');

async function testSimpleLogin() {
  try {
    console.log('🧪 Testing Email/Phone Login...\n');
    
    // Test 1: Login with existing admin email
    console.log('1. Testing login with admin EMAIL...');
    const emailResponse = await axios.post('http://localhost:3001/api/auth/login', {
      emailOrPhone: '<EMAIL>',
      password: 'AdminPass123'
    });
    
    if (emailResponse.data.success) {
      console.log('✅ Email login successful');
      console.log(`   User: ${emailResponse.data.data.user.name}`);
    }
    
    // Test 2: Try login with email using old format
    console.log('\n2. Testing login with EMAIL (old format)...');
    const oldEmailResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: '<EMAIL>',
      password: 'AdminPass123'
    });
    
    if (oldEmailResponse.data.success) {
      console.log('✅ Old email format login successful');
      console.log(`   User: ${oldEmailResponse.data.data.user.name}`);
    }
    
    console.log('\n🎉 Basic login tests completed!');
    console.log('\n📋 Ready to test in browser:');
    console.log('1. Open http://localhost:3000');
    console.log('2. Try login with: <EMAIL>');
    console.log('3. Password: AdminPass123');
    console.log('4. Field now accepts "Email or Phone Number"');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
  }
}

testSimpleLogin();
