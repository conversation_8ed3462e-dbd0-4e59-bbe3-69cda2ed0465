// Quick test to verify login is working
const axios = require('axios');

async function testLoginFix() {
  try {
    console.log('🧪 Testing Login Fix...\n');
    
    // Test Backend API
    console.log('1. Testing Backend API...');
    const response = await axios.post('http://localhost:3001/api/auth/login', {
      emailOrPhone: '<EMAIL>',
      password: 'AdminPass123'
    });
    
    if (response.data.success) {
      console.log('✅ Backend login working');
      console.log(`   User: ${response.data.data.user.name}`);
      console.log(`   Token: ${response.data.token ? 'Received' : 'Missing'}`);
    } else {
      console.log('❌ Backend login failed');
    }
    
    console.log('\n🎉 Backend Test Completed!');
    console.log('\n📋 Frontend Testing:');
    console.log('1. Open http://localhost:3000');
    console.log('2. Try login with: <EMAIL>');
    console.log('3. Password: AdminPass123');
    console.log('4. Check browser console for logs');
    console.log('5. Should see "🔄 Login form submitted with data"');
    
    console.log('\n🔧 If still not working:');
    console.log('- Check browser console for errors');
    console.log('- Try refreshing the page');
    console.log('- Make sure both servers are running');
    console.log('- Backend: http://localhost:3001');
    console.log('- Frontend: http://localhost:3000');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
  }
}

testLoginFix();
