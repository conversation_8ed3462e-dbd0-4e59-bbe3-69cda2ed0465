{"name": "SallaEcommerceApp", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/drawer": "^6.6.6", "expo": "~49.0.15", "expo-status-bar": "~1.6.0", "expo-linear-gradient": "~12.3.0", "expo-blur": "~12.4.1", "expo-image": "~1.3.5", "expo-font": "~11.4.0", "expo-splash-screen": "~0.20.5", "react": "18.2.0", "react-native": "0.72.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-svg": "13.9.0", "react-native-paper": "^5.11.1", "react-native-vector-icons": "^10.0.2", "react-native-animatable": "^1.3.3", "react-native-super-grid": "^4.9.6", "react-native-snap-carousel": "^3.9.1", "react-native-modal": "^13.0.1", "react-native-toast-message": "^2.1.6", "react-native-skeleton-placeholder": "^5.2.4", "react-native-shimmer": "^0.6.0", "axios": "^1.5.0", "react-query": "^3.39.3", "zustand": "^4.4.4", "react-hook-form": "^7.47.0", "yup": "^1.3.3", "date-fns": "^2.30.0", "react-native-dotenv": "^3.4.9"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "typescript": "^5.1.3"}, "private": true}