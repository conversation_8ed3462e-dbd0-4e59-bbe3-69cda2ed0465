import axios from 'axios';
import { API_BASE_URL } from '@env';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    // Token will be added by individual API calls
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      // This could trigger a logout
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: async (email, password) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  register: async (userData) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  logout: async (token) => {
    const response = await api.post('/auth/logout', {}, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  getProfile: async (token) => {
    const response = await api.get('/auth/me', {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  updateProfile: async (userData, token) => {
    const response = await api.patch('/auth/update-profile', userData, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },
};

// Products API
export const productsAPI = {
  getProducts: async (params = {}) => {
    const response = await api.get('/products', { params });
    return response.data;
  },

  getProduct: async (id) => {
    const response = await api.get(`/products/${id}`);
    return response.data;
  },

  searchProducts: async (query, filters = {}) => {
    const response = await api.get('/products', {
      params: { search: query, ...filters }
    });
    return response.data;
  },
};

// Categories API
export const categoriesAPI = {
  getCategories: async () => {
    const response = await api.get('/categories');
    return response.data;
  },

  getCategory: async (id) => {
    const response = await api.get(`/categories/${id}`);
    return response.data;
  },

  getCategoryProducts: async (id, params = {}) => {
    const response = await api.get(`/categories/${id}/products`, { params });
    return response.data;
  },
};

// Cart API
export const cartAPI = {
  getCart: async (token) => {
    const response = await api.get('/cart', {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  addToCart: async (item, token) => {
    const response = await api.post('/cart/items', item, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  updateCartItem: async (itemId, quantity, token) => {
    const response = await api.patch(`/cart/items/${itemId}`, { quantity }, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  removeFromCart: async (itemId, token) => {
    const response = await api.delete(`/cart/items/${itemId}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  clearCart: async (token) => {
    const response = await api.delete('/cart/clear', {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },
};

// Orders API
export const ordersAPI = {
  getOrders: async (token) => {
    const response = await api.get('/orders', {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  getOrder: async (id, token) => {
    const response = await api.get(`/orders/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  createOrder: async (orderData, token) => {
    const response = await api.post('/orders', orderData, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  cancelOrder: async (id, token) => {
    const response = await api.patch(`/orders/${id}/cancel`, {}, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },
};

// Coupons API
export const couponsAPI = {
  validateCoupon: async (code, token) => {
    const response = await api.post('/coupons/validate', { code }, {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },
};

// Store API
export const storeAPI = {
  getStoreInfo: async () => {
    const response = await api.get('/store/info');
    return response.data;
  },

  getPaymentMethods: async () => {
    const response = await api.get('/payments/methods');
    return response.data;
  },

  getShippingMethods: async () => {
    const response = await api.get('/shipping/methods');
    return response.data;
  },
};

export default api;
