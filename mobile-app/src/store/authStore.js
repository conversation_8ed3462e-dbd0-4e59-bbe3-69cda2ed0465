import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { authAPI } from '../services/api';

const useAuthStore = create((set, get) => ({
  // State
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,

  // Actions
  initialize: async () => {
    try {
      set({ isLoading: true });
      
      // Get token from storage
      const token = await AsyncStorage.getItem('token');
      
      if (token) {
        // Verify token and get user data
        const userData = await authAPI.getProfile(token);
        
        set({
          user: userData.user,
          token,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });
      } else {
        set({ isLoading: false });
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      // Clear invalid token
      await AsyncStorage.removeItem('token');
      set({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    }
  },

  login: async (email, password) => {
    try {
      set({ isLoading: true, error: null });
      
      const response = await authAPI.login(email, password);
      const { user, token } = response;
      
      // Store token
      await AsyncStorage.setItem('token', token);
      
      set({
        user,
        token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });
      
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Login failed';
      set({
        isLoading: false,
        error: errorMessage,
      });
      return { success: false, error: errorMessage };
    }
  },

  register: async (userData) => {
    try {
      set({ isLoading: true, error: null });
      
      const response = await authAPI.register(userData);
      const { user, token } = response;
      
      // Store token
      await AsyncStorage.setItem('token', token);
      
      set({
        user,
        token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });
      
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Registration failed';
      set({
        isLoading: false,
        error: errorMessage,
      });
      return { success: false, error: errorMessage };
    }
  },

  logout: async () => {
    try {
      // Remove token from storage
      await AsyncStorage.removeItem('token');
      
      // Call logout API if needed
      const { token } = get();
      if (token) {
        await authAPI.logout(token);
      }
      
      set({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
      
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      // Still clear local state even if API call fails
      set({
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
      return { success: true };
    }
  },

  updateProfile: async (userData) => {
    try {
      set({ isLoading: true, error: null });
      
      const { token } = get();
      const response = await authAPI.updateProfile(userData, token);
      
      set({
        user: response.user,
        isLoading: false,
        error: null,
      });
      
      return { success: true };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Profile update failed';
      set({
        isLoading: false,
        error: errorMessage,
      });
      return { success: false, error: errorMessage };
    }
  },

  clearError: () => {
    set({ error: null });
  },

  // Getters
  getToken: () => get().token,
  getUser: () => get().user,
  isLoggedIn: () => get().isAuthenticated,
}));

export { useAuthStore };
