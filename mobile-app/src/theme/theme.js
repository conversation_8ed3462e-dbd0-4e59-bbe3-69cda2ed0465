import { DefaultTheme } from 'react-native-paper';

// Modern Social Media Inspired Color Palette
export const colors = {
  // Primary Colors (Instagram/TikTok inspired)
  primary: '#E91E63', // Pink
  primaryDark: '#C2185B',
  primaryLight: '#F8BBD9',
  
  // Secondary Colors (Modern gradient)
  secondary: '#9C27B0', // Purple
  secondaryDark: '#7B1FA2',
  secondaryLight: '#E1BEE7',
  
  // Accent Colors (Social media vibes)
  accent: '#FF6B35', // Orange
  accentDark: '#E55722',
  accentLight: '#FFB299',
  
  // Neutral Colors (Clean and modern)
  background: '#FAFAFA',
  surface: '#FFFFFF',
  surfaceDark: '#F5F5F5',
  
  // Text Colors
  text: '#212121',
  textSecondary: '#757575',
  textLight: '#BDBDBD',
  textOnPrimary: '#FFFFFF',
  
  // Status Colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  
  // Social Colors
  facebook: '#1877F2',
  instagram: '#E4405F',
  twitter: '#1DA1F2',
  whatsapp: '#25D366',
  
  // Gradient Colors
  gradientStart: '#E91E63',
  gradientEnd: '#9C27B0',
  
  // Border and Divider
  border: '#E0E0E0',
  divider: '#EEEEEE',
  
  // Shadow
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowDark: 'rgba(0, 0, 0, 0.2)',
};

// Typography (Modern and clean)
export const typography = {
  fontFamily: {
    regular: 'Poppins-Regular',
    medium: 'Poppins-Medium',
    semiBold: 'Poppins-SemiBold',
    bold: 'Poppins-Bold',
  },
  fontSize: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 28,
    display: 32,
  },
  lineHeight: {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    xxl: 36,
    xxxl: 40,
    display: 48,
  },
};

// Spacing (Consistent spacing system)
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

// Border Radius (Modern rounded corners)
export const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  round: 50,
};

// Shadows (Subtle and modern)
export const shadows = {
  small: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  medium: {
    shadowColor: colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6.27,
    elevation: 10,
  },
  large: {
    shadowColor: colors.shadowDark,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.2,
    shadowRadius: 10.32,
    elevation: 16,
  },
};

// React Native Paper Theme
export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.primary,
    accent: colors.accent,
    background: colors.background,
    surface: colors.surface,
    text: colors.text,
    disabled: colors.textLight,
    placeholder: colors.textSecondary,
    backdrop: 'rgba(0, 0, 0, 0.5)',
    onSurface: colors.text,
    notification: colors.error,
  },
  fonts: {
    regular: {
      fontFamily: typography.fontFamily.regular,
      fontWeight: 'normal',
    },
    medium: {
      fontFamily: typography.fontFamily.medium,
      fontWeight: 'normal',
    },
    light: {
      fontFamily: typography.fontFamily.regular,
      fontWeight: 'normal',
    },
    thin: {
      fontFamily: typography.fontFamily.regular,
      fontWeight: 'normal',
    },
  },
  roundness: borderRadius.md,
};

// Component Styles (Reusable style objects)
export const componentStyles = {
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  card: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    ...shadows.small,
  },
  button: {
    borderRadius: borderRadius.md,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
  },
  input: {
    borderRadius: borderRadius.sm,
    borderWidth: 1,
    borderColor: colors.border,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.fontSize.md,
    fontFamily: typography.fontFamily.regular,
  },
  gradient: {
    flex: 1,
    borderRadius: borderRadius.lg,
  },
};
